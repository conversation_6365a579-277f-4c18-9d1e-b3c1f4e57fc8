(()=>{var e={};e.id=983,e.ids=[983],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10401:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>p,routeModule:()=>u,tree:()=>d});var s=t(65239),a=t(48088),o=t(88170),n=t.n(o),i=t(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(r,l);let d={children:["",{children:["auth",{children:["register",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,59401)),"C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\app\\auth\\register\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,p=["C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\app\\auth\\register\\page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/auth/register/page",pathname:"/auth/register",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15215:(e,r,t)=>{Promise.resolve().then(t.bind(t,59401))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23463:(e,r,t)=>{Promise.resolve().then(t.bind(t,24507))},24507:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>g});var s=t(60687),a=t(43210),o=t(16189),n=t(85814),i=t.n(n),l=t(27605),d=t(57335),p=t(52581),c=t(82136),u=t(31923),m=t(13977),h=t(35810);let g=()=>{let e=(0,o.useRouter)(),[r,t]=(0,a.useState)(!1),[n,g]=(0,a.useState)(!1),{handleSubmit:f,formState:{errors:x},setValue:v,watch:y}=(0,l.mN)({resolver:(0,d.u)(h.zK),defaultValues:{firstName:"",lastName:"",email:"",password:"",confirmPassword:"",role:"",phone:"",acceptTerms:!1}}),b=y(),C=async r=>{try{t(!0),console.log("Registration data:",r),p.oR.success("Registration successful!",{description:"Please check your email to verify your account."}),e.push("/auth/login?message=registration-success")}catch(e){p.oR.error("Registration failed",{description:e instanceof Error?e.message:"Please try again."})}finally{t(!1)}},w=async()=>{try{g(!0);let r=await (0,c.signIn)("google",{callbackUrl:"/dashboard",redirect:!1});r?.error?p.oR.error("OAuth registration failed",{description:"Please try again or use the registration form."}):r?.url&&(p.oR.success("Registration successful!",{description:"Welcome to DentCare Pro!"}),e.push(r.url))}catch(e){console.error("OAuth registration error:",e),p.oR.error("OAuth registration failed",{description:"Please try again or use the registration form."})}finally{g(!1)}};return(0,s.jsx)(u.A,{title:"Create your account",subtitle:"Join DentCare Pro and start managing your dental care",children:(0,s.jsxs)("form",{onSubmit:f(C),className:"space-y-6",children:[(0,s.jsx)(m.ID,{provider:"google",isLoading:n,disabled:r||n,onClick:w,children:"Sign up with Google"}),(0,s.jsx)(m.Sq,{text:"or sign up with email"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[(0,s.jsx)(m.vy,{label:"First name",type:"text",placeholder:"Enter your first name",value:b.firstName,onChange:e=>v("firstName",e),error:x.firstName?.message,required:!0,autoComplete:"given-name"}),(0,s.jsx)(m.vy,{label:"Last name",type:"text",placeholder:"Enter your last name",value:b.lastName,onChange:e=>v("lastName",e),error:x.lastName?.message,required:!0,autoComplete:"family-name"})]}),(0,s.jsx)(m.vy,{label:"Email address",type:"email",placeholder:"Enter your email",value:b.email,onChange:e=>v("email",e),error:x.email?.message,required:!0,autoComplete:"email"}),(0,s.jsx)(m.vy,{label:"Phone number",type:"tel",placeholder:"Enter your phone number (optional)",value:b.phone||"",onChange:e=>v("phone",e),error:x.phone?.message,autoComplete:"tel"}),(0,s.jsx)(m.QT,{value:b.role,onChange:e=>v("role",e),error:x.role?.message,disabled:r||n}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(m.vy,{label:"Password",type:"password",placeholder:"Create a strong password",value:b.password,onChange:e=>v("password",e),error:x.password?.message,required:!0,autoComplete:"new-password"}),(0,s.jsx)(m.vy,{label:"Confirm password",type:"password",placeholder:"Confirm your password",value:b.confirmPassword,onChange:e=>v("confirmPassword",e),error:x.confirmPassword?.message,required:!0,autoComplete:"new-password"})]}),(0,s.jsx)(m.PD,{checked:b.acceptTerms,onChange:e=>v("acceptTerms",e),error:x.acceptTerms?.message,disabled:r||n}),(0,s.jsx)(m.FX,{isLoading:r,disabled:r||n,loadingText:"Creating account...",children:"Create account"}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Already have an account?"," ",(0,s.jsx)(i(),{href:"/auth/login",className:"text-primary hover:text-primary/80 transition-colors font-medium",children:"Sign in"})]})})]})})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},59401:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\test\\\\dentist-appointment-platform\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\app\\auth\\register\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,97,658,665,513,383],()=>t(10401));module.exports=s})();