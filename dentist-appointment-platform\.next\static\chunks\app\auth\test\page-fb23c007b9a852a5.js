(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[890],{704:(e,s,a)=>{Promise.resolve().then(a.bind(a,2756))},2756:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>m});var t=a(5155);a(2115);var i=a(2108),n=a(3294),r=a(285),c=a(6695),l=a(6126),d=a(1007),o=a(5525),h=a(9803),x=a(646),u=a(4861),g=a(5339);let m=()=>{let{data:e,status:s}=(0,i.useSession)(),{user:a,isAuthenticated:m,isLoading:j}=(0,n.As)(),v=[{name:"NextAuth Session",status:"authenticated"===s?"pass":"loading"===s?"loading":"fail",description:"Status: ".concat(s),icon:d.A},{name:"Zustand Store Sync",status:m&&a?"pass":j?"loading":"fail",description:"Authenticated: ".concat(m,", User: ").concat(a?"Present":"None"),icon:o.A},{name:"Route Protection",status:"pass",description:"Auth test page accessible",icon:h.A}],p=e=>{switch(e){case"pass":return(0,t.jsx)(x.A,{className:"w-5 h-5 text-green-500"});case"fail":return(0,t.jsx)(u.A,{className:"w-5 h-5 text-red-500"});case"loading":return(0,t.jsx)(g.A,{className:"w-5 h-5 text-yellow-500"});default:return(0,t.jsx)(g.A,{className:"w-5 h-5 text-gray-500"})}},f=e=>{switch(e){case"pass":return(0,t.jsx)(l.E,{className:"bg-green-100 text-green-800",children:"Pass"});case"fail":return(0,t.jsx)(l.E,{variant:"destructive",children:"Fail"});case"loading":return(0,t.jsx)(l.E,{variant:"secondary",children:"Loading"});default:return(0,t.jsx)(l.E,{variant:"outline",children:"Unknown"})}};return(0,t.jsxs)("div",{className:"container mx-auto p-6 space-y-6",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-foreground mb-2",children:"Authentication System Test"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Verify all authentication components are working correctly"})]}),(0,t.jsx)("div",{className:"grid gap-4 md:grid-cols-3",children:v.map((e,s)=>{let a=e.icon;return(0,t.jsxs)(c.Zp,{className:"glass-card",children:[(0,t.jsx)(c.aR,{className:"pb-3",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(a,{className:"w-5 h-5"}),(0,t.jsx)(c.ZB,{className:"text-sm",children:e.name})]}),p(e.status)]})}),(0,t.jsxs)(c.Wu,{children:[(0,t.jsx)(c.BT,{className:"text-xs mb-2",children:e.description}),f(e.status)]})]},s)})}),(0,t.jsxs)(c.Zp,{className:"glass-card",children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{children:"Session Information"}),(0,t.jsx)(c.BT,{children:"Current authentication state"})]}),(0,t.jsx)(c.Wu,{className:"space-y-4",children:(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold mb-2",children:"NextAuth Session"}),(0,t.jsx)("pre",{className:"text-xs bg-muted p-3 rounded overflow-auto",children:JSON.stringify(e,null,2)})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold mb-2",children:"Zustand Store"}),(0,t.jsx)("pre",{className:"text-xs bg-muted p-3 rounded overflow-auto",children:JSON.stringify({user:a,isAuthenticated:m,isLoading:j},null,2)})]})]})})]}),(0,t.jsxs)(c.Zp,{className:"glass-card",children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{children:"Authentication Actions"}),(0,t.jsx)(c.BT,{children:"Test authentication functionality"})]}),(0,t.jsxs)(c.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex flex-wrap gap-3",children:[!e&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(r.$,{onClick:()=>(0,i.signIn)("credentials"),variant:"outline",children:"Test Credentials Login"}),(0,t.jsx)(r.$,{onClick:()=>(0,i.signIn)("google"),className:"glass-button",children:"Test Google OAuth"})]}),e&&(0,t.jsx)(r.$,{onClick:()=>(0,i.signOut)(),variant:"destructive",children:"Sign Out"})]}),a&&(0,t.jsxs)("div",{className:"mt-4 p-4 bg-muted/50 rounded-lg",children:[(0,t.jsx)("h4",{className:"font-semibold mb-2",children:"User Details"}),(0,t.jsxs)("div",{className:"grid gap-2 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"ID:"})," ",a.id]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Email:"})," ",a.email]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Name:"})," ",a.name]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Role:"})," ",a.role]}),a.image&&(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Image:"})," ",a.image]})]})]})]})]}),(0,t.jsxs)(c.Zp,{className:"glass-card",children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{children:"Navigation Test"}),(0,t.jsx)(c.BT,{children:"Test protected routes and redirects"})]}),(0,t.jsx)(c.Wu,{children:(0,t.jsxs)("div",{className:"flex flex-wrap gap-3",children:[(0,t.jsx)(r.$,{onClick:()=>window.location.href="/dashboard",variant:"outline",children:"Test Dashboard (Protected)"}),(0,t.jsx)(r.$,{onClick:()=>window.location.href="/auth/login",variant:"outline",children:"Test Login Page"}),(0,t.jsx)(r.$,{onClick:()=>window.location.href="/auth/register",variant:"outline",children:"Test Register Page"}),(0,t.jsx)(r.$,{onClick:()=>window.location.href="/unauthorized",variant:"outline",children:"Test Unauthorized Page"})]})})]})]})}},6126:(e,s,a)=>{"use strict";a.d(s,{E:()=>l});var t=a(5155);a(2115);var i=a(9708),n=a(2085),r=a(2911);let c=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:s,variant:a,asChild:n=!1,...l}=e,d=n?i.DX:"span";return(0,t.jsx)(d,{"data-slot":"badge",className:(0,r.cn)(c({variant:a}),s),...l})}}},e=>{var s=s=>e(e.s=s);e.O(0,[52,733,60,441,684,358],()=>s(704)),_N_E=e.O()}]);