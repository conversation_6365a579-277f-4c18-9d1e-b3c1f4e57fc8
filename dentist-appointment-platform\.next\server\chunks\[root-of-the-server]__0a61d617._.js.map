{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 140, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/test/dentist-appointment-platform/src/types/auth.ts"], "sourcesContent": ["// Authentication and User Types\nexport enum UserRole {\n  PATIENT = 'PATIENT',\n  DENTIST = 'DENTIST',\n  ADMIN = 'ADMIN'\n}\n\nexport interface User {\n  id: string;\n  email: string;\n  name?: string;\n  image?: string;\n  role: UserRole;\n  createdAt: Date;\n  updatedAt: Date;\n  \n  // Relations\n  patientProfile?: PatientProfile;\n  dentistProfile?: DentistProfile;\n}\n\nexport interface PatientProfile {\n  id: string;\n  userId: string;\n  dateOfBirth?: Date;\n  phone?: string;\n  address?: string;\n  emergencyContact?: string;\n  medicalHistory?: string;\n  allergies?: string[];\n  insuranceInfo?: InsuranceInfo;\n  createdAt: Date;\n  updatedAt: Date;\n  \n  // Relations\n  user: User;\n}\n\nexport interface DentistProfile {\n  id: string;\n  userId: string;\n  licenseNumber: string;\n  specialization: string[];\n  workingHours: WorkingHours;\n  practiceInfo?: PracticeInfo;\n  createdAt: Date;\n  updatedAt: Date;\n  \n  // Relations\n  user: User;\n}\n\nexport interface InsuranceInfo {\n  provider: string;\n  policyNumber: string;\n  groupNumber?: string;\n  expirationDate?: Date;\n}\n\nexport interface WorkingHours {\n  monday?: WorkingTimeSlot[];\n  tuesday?: WorkingTimeSlot[];\n  wednesday?: WorkingTimeSlot[];\n  thursday?: WorkingTimeSlot[];\n  friday?: WorkingTimeSlot[];\n  saturday?: WorkingTimeSlot[];\n  sunday?: WorkingTimeSlot[];\n}\n\nexport interface WorkingTimeSlot {\n  start: string; // HH:MM format\n  end: string;   // HH:MM format\n}\n\nexport interface PracticeInfo {\n  name: string;\n  address: string;\n  phone: string;\n  email?: string;\n  website?: string;\n}\n\n// Authentication Session Types\nexport interface AuthSession {\n  user: User;\n  accessToken: string;\n  refreshToken?: string;\n  expiresAt: Date;\n}\n\n// NextAuth session extension\ndeclare module 'next-auth' {\n  interface Session {\n    user: User;\n    accessToken: string;\n  }\n\n  interface User {\n    id: string;\n    email: string;\n    name: string;\n    role: UserRole;\n    image?: string;\n  }\n}\n\ndeclare module 'next-auth/jwt' {\n  interface JWT {\n    id: string;\n    role: UserRole;\n    accessToken: string;\n  }\n}\n\nexport interface LoginCredentials {\n  email: string;\n  password: string;\n}\n\nexport interface RegisterData {\n  email: string;\n  name: string;\n  role: UserRole;\n  password: string;\n  confirmPassword: string;\n}\n\n// Permission Types\nexport type Permission = \n  | 'appointments:read:own'\n  | 'appointments:read:all'\n  | 'appointments:create:own'\n  | 'appointments:create:all'\n  | 'appointments:update:own'\n  | 'appointments:update:all'\n  | 'appointments:delete:own'\n  | 'appointments:delete:all'\n  | 'patients:read:own'\n  | 'patients:read:all'\n  | 'patients:update:own'\n  | 'patients:update:all'\n  | 'documents:read:own'\n  | 'documents:read:all'\n  | 'documents:create:own'\n  | 'documents:create:all'\n  | 'documents:update:own'\n  | 'documents:update:all'\n  | 'documents:delete:own'\n  | 'documents:delete:all'\n  | 'profile:read:own'\n  | 'profile:update:own'\n  | 'practice:manage'\n  | 'users:manage';\n\nexport interface RolePermissions {\n  [UserRole.PATIENT]: Permission[];\n  [UserRole.DENTIST]: Permission[];\n  [UserRole.ADMIN]: Permission[];\n}\n\n// API Response Types\nexport interface AuthResponse {\n  success: boolean;\n  user?: User;\n  token?: string;\n  message?: string;\n  errors?: string[];\n}\n\nexport interface ProfileUpdateData {\n  name?: string;\n  phone?: string;\n  address?: string;\n  emergencyContact?: string;\n  dateOfBirth?: Date;\n  allergies?: string[];\n  medicalHistory?: string;\n  insuranceInfo?: InsuranceInfo;\n}\n"], "names": [], "mappings": "AAAA,gCAAgC;;;;AACzB,IAAA,AAAK,kCAAA;;;;WAAA", "debugId": null}}, {"offset": {"line": 156, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/test/dentist-appointment-platform/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth';\nimport GoogleProvider from 'next-auth/providers/google';\nimport CredentialsProvider from 'next-auth/providers/credentials';\nimport { UserRole } from '@/types/auth';\n\nexport const authOptions: NextAuthOptions = {\n  providers: [\n    GoogleProvider({\n      clientId: process.env.GOOGLE_CLIENT_ID!,\n      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,\n      authorization: {\n        params: {\n          prompt: \"consent\",\n          access_type: \"offline\",\n          response_type: \"code\"\n        }\n      }\n    }),\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null;\n        }\n\n        try {\n          // TODO: Replace with actual API call to your backend\n          const response = await fetch(`${process.env.NEXTAUTH_URL}/api/auth/login`, {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json',\n            },\n            body: JSON.stringify({\n              email: credentials.email,\n              password: credentials.password,\n            }),\n          });\n\n          if (!response.ok) {\n            return null;\n          }\n\n          const user = await response.json();\n\n          if (user && user.id) {\n            return {\n              id: user.id,\n              email: user.email,\n              name: user.name,\n              role: user.role,\n              image: user.image,\n            };\n          }\n\n          return null;\n        } catch (error) {\n          console.error('Auth error:', error);\n          return null;\n        }\n      }\n    })\n  ],\n  \n  callbacks: {\n    async jwt({ token, user, account }) {\n      // Initial sign in\n      if (account && user) {\n        token.accessToken = account.access_token || '';\n        token.role = user.role || UserRole.PATIENT; // Default role\n        token.id = user.id;\n      }\n\n      return token;\n    },\n    \n    async session({ session, token }) {\n      // Send properties to the client\n      if (token) {\n        session.user.id = token.id as string;\n        session.user.role = token.role as UserRole;\n        session.accessToken = token.accessToken as string;\n      }\n\n      return session;\n    },\n\n    async signIn({ user, account, profile }) {\n      // Handle Google OAuth sign in\n      if (account?.provider === 'google') {\n        try {\n          // TODO: Check if user exists in your database\n          // If not, create a new user with default role\n          // For now, we'll allow all Google sign-ins\n          \n          // Set default role if not set\n          if (!user.role) {\n            user.role = UserRole.PATIENT;\n          }\n\n          return true;\n        } catch (error) {\n          console.error('Google sign in error:', error);\n          return false;\n        }\n      }\n\n      return true;\n    },\n\n    async redirect({ url, baseUrl }) {\n      // Allows relative callback URLs\n      if (url.startsWith(\"/\")) return `${baseUrl}${url}`;\n      // Allows callback URLs on the same origin\n      else if (new URL(url).origin === baseUrl) return url;\n      return baseUrl;\n    }\n  },\n\n  pages: {\n    signIn: '/auth/login',\n    error: '/auth/error',\n  },\n\n  session: {\n    strategy: 'jwt',\n    maxAge: 24 * 60 * 60, // 24 hours\n  },\n\n  jwt: {\n    maxAge: 24 * 60 * 60, // 24 hours\n  },\n\n  secret: process.env.NEXTAUTH_SECRET,\n\n  debug: process.env.NODE_ENV === 'development',\n};\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;;;;AAEO,MAAM,cAA+B;IAC1C,WAAW;QACT,CAAA,GAAA,qJAAA,CAAA,UAAc,AAAD,EAAE;YACb,UAAU,QAAQ,GAAG,CAAC,gBAAgB;YACtC,cAAc,QAAQ,GAAG,CAAC,oBAAoB;YAC9C,eAAe;gBACb,QAAQ;oBACN,QAAQ;oBACR,aAAa;oBACb,eAAe;gBACjB;YACF;QACF;QACA,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,IAAI;oBACF,qDAAqD;oBACrD,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,GAAG,CAAC,YAAY,CAAC,eAAe,CAAC,EAAE;wBACzE,QAAQ;wBACR,SAAS;4BACP,gBAAgB;wBAClB;wBACA,MAAM,KAAK,SAAS,CAAC;4BACnB,OAAO,YAAY,KAAK;4BACxB,UAAU,YAAY,QAAQ;wBAChC;oBACF;oBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;wBAChB,OAAO;oBACT;oBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;oBAEhC,IAAI,QAAQ,KAAK,EAAE,EAAE;wBACnB,OAAO;4BACL,IAAI,KAAK,EAAE;4BACX,OAAO,KAAK,KAAK;4BACjB,MAAM,KAAK,IAAI;4BACf,MAAM,KAAK,IAAI;4BACf,OAAO,KAAK,KAAK;wBACnB;oBACF;oBAEA,OAAO;gBACT,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,eAAe;oBAC7B,OAAO;gBACT;YACF;QACF;KACD;IAED,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE;YAChC,kBAAkB;YAClB,IAAI,WAAW,MAAM;gBACnB,MAAM,WAAW,GAAG,QAAQ,YAAY,IAAI;gBAC5C,MAAM,IAAI,GAAG,KAAK,IAAI,IAAI,sHAAA,CAAA,WAAQ,CAAC,OAAO,EAAE,eAAe;gBAC3D,MAAM,EAAE,GAAG,KAAK,EAAE;YACpB;YAEA,OAAO;QACT;QAEA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,gCAAgC;YAChC,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE;gBAC1B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,WAAW,GAAG,MAAM,WAAW;YACzC;YAEA,OAAO;QACT;QAEA,MAAM,QAAO,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE;YACrC,8BAA8B;YAC9B,IAAI,SAAS,aAAa,UAAU;gBAClC,IAAI;oBACF,8CAA8C;oBAC9C,8CAA8C;oBAC9C,2CAA2C;oBAE3C,8BAA8B;oBAC9B,IAAI,CAAC,KAAK,IAAI,EAAE;wBACd,KAAK,IAAI,GAAG,sHAAA,CAAA,WAAQ,CAAC,OAAO;oBAC9B;oBAEA,OAAO;gBACT,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,yBAAyB;oBACvC,OAAO;gBACT;YACF;YAEA,OAAO;QACT;QAEA,MAAM,UAAS,EAAE,GAAG,EAAE,OAAO,EAAE;YAC7B,gCAAgC;YAChC,IAAI,IAAI,UAAU,CAAC,MAAM,OAAO,GAAG,UAAU,KAAK;iBAE7C,IAAI,IAAI,IAAI,KAAK,MAAM,KAAK,SAAS,OAAO;YACjD,OAAO;QACT;IACF;IAEA,OAAO;QACL,QAAQ;QACR,OAAO;IACT;IAEA,SAAS;QACP,UAAU;QACV,QAAQ,KAAK,KAAK;IACpB;IAEA,KAAK;QACH,QAAQ,KAAK,KAAK;IACpB;IAEA,QAAQ,QAAQ,GAAG,CAAC,eAAe;IAEnC,OAAO,oDAAyB;AAClC", "debugId": null}}, {"offset": {"line": 292, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/test/dentist-appointment-platform/src/app/api/auth/%5B...nextauth%5D/route.ts"], "sourcesContent": ["import NextAuth from 'next-auth';\nimport { authOptions } from '@/lib/auth';\n\nconst handler = NextAuth(authOptions);\n\nexport { handler as GET, handler as POST };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,MAAM,UAAU,CAAA,GAAA,uIAAA,CAAA,UAAQ,AAAD,EAAE,oHAAA,CAAA,cAAW", "debugId": null}}]}