[{"C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\app\\api\\auth\\login\\route.ts": "1", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\app\\api\\auth\\[...nextauth]\\route.ts": "2", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\app\\auth\\login\\page.tsx": "3", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\app\\auth\\register\\page.tsx": "4", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\app\\auth\\test\\page.tsx": "5", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\app\\dashboard\\page.tsx": "6", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\app\\layout.tsx": "7", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\app\\page.tsx": "8", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\app\\unauthorized\\page.tsx": "9", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\common\\ErrorBoundary.tsx": "10", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\common\\Loading.tsx": "11", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\common\\ProtectedRoute.tsx": "12", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\forms\\AuthFormField.tsx": "13", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\forms\\AuthSubmitButton.tsx": "14", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\forms\\FormDivider.tsx": "15", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\forms\\index.ts": "16", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\forms\\OAuthButton.tsx": "17", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\forms\\RoleSelector.tsx": "18", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\forms\\TermsCheckbox.tsx": "19", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\layout\\AppLayout.tsx": "20", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\layout\\AuthLayout.tsx": "21", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\layout\\Breadcrumbs.tsx": "22", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\layout\\Header.tsx": "23", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\layout\\PublicHeader.tsx": "24", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\layout\\Sidebar.tsx": "25", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\providers\\MockAuthProvider.tsx": "26", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\providers\\NextAuthProvider.tsx": "27", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\providers\\SessionSync.tsx": "28", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\ui\\avatar.tsx": "29", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\ui\\badge.tsx": "30", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\ui\\button.tsx": "31", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\ui\\card.tsx": "32", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\ui\\dialog.tsx": "33", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\ui\\dropdown-menu.tsx": "34", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\ui\\form.tsx": "35", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\ui\\input.tsx": "36", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\ui\\label.tsx": "37", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\ui\\select.tsx": "38", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\ui\\sonner.tsx": "39", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\ui\\textarea.tsx": "40", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\hooks\\useApi.ts": "41", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\hooks\\useForm.ts": "42", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\hooks\\useLocalStorage.ts": "43", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\hooks\\useRouteProtection.ts": "44", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\lib\\auth.ts": "45", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\lib\\constants.ts": "46", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\lib\\utils.ts": "47", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\lib\\validations\\auth.ts": "48", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\store\\authStore.ts": "49", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\store\\uiStore.ts": "50", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\types\\appointment.ts": "51", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\types\\auth.ts": "52", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\types\\common.ts": "53", "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\types\\document.ts": "54"}, {"size": 1642, "mtime": 1750116808523, "results": "55", "hashOfConfig": "56"}, {"size": 161, "mtime": 1750113908894, "results": "57", "hashOfConfig": "56"}, {"size": 5280, "mtime": 1750116819768, "results": "58", "hashOfConfig": "56"}, {"size": 6715, "mtime": 1750117550187, "results": "59", "hashOfConfig": "56"}, {"size": 7228, "mtime": 1750116970908, "results": "60", "hashOfConfig": "56"}, {"size": 9576, "mtime": 1750116987261, "results": "61", "hashOfConfig": "56"}, {"size": 2304, "mtime": 1750115969632, "results": "62", "hashOfConfig": "56"}, {"size": 29498, "mtime": 1750114929108, "results": "63", "hashOfConfig": "56"}, {"size": 3156, "mtime": 1750117098269, "results": "64", "hashOfConfig": "56"}, {"size": 10209, "mtime": 1750117135514, "results": "65", "hashOfConfig": "56"}, {"size": 6919, "mtime": 1750112331063, "results": "66", "hashOfConfig": "56"}, {"size": 2542, "mtime": 1750117212583, "results": "67", "hashOfConfig": "56"}, {"size": 2700, "mtime": 1750113209018, "results": "68", "hashOfConfig": "56"}, {"size": 1270, "mtime": 1750113218545, "results": "69", "hashOfConfig": "56"}, {"size": 629, "mtime": 1750113251258, "results": "70", "hashOfConfig": "56"}, {"size": 356, "mtime": 1750113350968, "results": "71", "hashOfConfig": "56"}, {"size": 3439, "mtime": 1750113242213, "results": "72", "hashOfConfig": "56"}, {"size": 3090, "mtime": 1750117509913, "results": "73", "hashOfConfig": "56"}, {"size": 2369, "mtime": 1750113342370, "results": "74", "hashOfConfig": "56"}, {"size": 6068, "mtime": 1750117177913, "results": "75", "hashOfConfig": "56"}, {"size": 6582, "mtime": 1750113176815, "results": "76", "hashOfConfig": "56"}, {"size": 7193, "mtime": 1750117816280, "results": "77", "hashOfConfig": "56"}, {"size": 10237, "mtime": 1750117265223, "results": "78", "hashOfConfig": "56"}, {"size": 3151, "mtime": 1750113440702, "results": "79", "hashOfConfig": "56"}, {"size": 10316, "mtime": 1750117859797, "results": "80", "hashOfConfig": "56"}, {"size": 1894, "mtime": 1750117903016, "results": "81", "hashOfConfig": "56"}, {"size": 385, "mtime": 1750114076165, "results": "82", "hashOfConfig": "56"}, {"size": 1383, "mtime": 1750114066516, "results": "83", "hashOfConfig": "56"}, {"size": 1097, "mtime": 1750112453231, "results": "84", "hashOfConfig": "56"}, {"size": 1631, "mtime": 1750112453258, "results": "85", "hashOfConfig": "56"}, {"size": 2123, "mtime": 1750110717122, "results": "86", "hashOfConfig": "56"}, {"size": 1989, "mtime": 1750110717140, "results": "87", "hashOfConfig": "56"}, {"size": 3982, "mtime": 1750110717156, "results": "88", "hashOfConfig": "56"}, {"size": 8284, "mtime": 1750110717254, "results": "89", "hashOfConfig": "56"}, {"size": 3759, "mtime": 1750110717190, "results": "90", "hashOfConfig": "56"}, {"size": 967, "mtime": 1750110717202, "results": "91", "hashOfConfig": "56"}, {"size": 611, "mtime": 1750110717197, "results": "92", "hashOfConfig": "56"}, {"size": 6253, "mtime": 1750110717218, "results": "93", "hashOfConfig": "56"}, {"size": 564, "mtime": 1750110717232, "results": "94", "hashOfConfig": "56"}, {"size": 759, "mtime": 1750110717223, "results": "95", "hashOfConfig": "56"}, {"size": 8951, "mtime": 1750112299008, "results": "96", "hashOfConfig": "56"}, {"size": 9958, "mtime": 1750112237206, "results": "97", "hashOfConfig": "56"}, {"size": 6650, "mtime": 1750112263480, "results": "98", "hashOfConfig": "56"}, {"size": 3353, "mtime": 1750113570949, "results": "99", "hashOfConfig": "56"}, {"size": 3608, "mtime": 1750117977480, "results": "100", "hashOfConfig": "56"}, {"size": 6881, "mtime": 1750111327757, "results": "101", "hashOfConfig": "56"}, {"size": 8547, "mtime": 1750112595883, "results": "102", "hashOfConfig": "56"}, {"size": 2951, "mtime": 1750117432426, "results": "103", "hashOfConfig": "56"}, {"size": 7746, "mtime": 1750114050164, "results": "104", "hashOfConfig": "56"}, {"size": 8508, "mtime": 1750117650960, "results": "105", "hashOfConfig": "56"}, {"size": 5294, "mtime": 1750110931914, "results": "106", "hashOfConfig": "56"}, {"size": 3466, "mtime": 1750118027396, "results": "107", "hashOfConfig": "56"}, {"size": 6157, "mtime": 1750110989648, "results": "108", "hashOfConfig": "56"}, {"size": 6311, "mtime": 1750110961139, "results": "109", "hashOfConfig": "56"}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "7ns715", {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 19, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\app\\api\\auth\\login\\route.ts", [], ["272"], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\app\\api\\auth\\[...nextauth]\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\app\\auth\\login\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\app\\auth\\register\\page.tsx", ["273", "274", "275"], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\app\\auth\\test\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\app\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\app\\unauthorized\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\common\\ErrorBoundary.tsx", [], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\common\\Loading.tsx", [], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\common\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\forms\\AuthFormField.tsx", [], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\forms\\AuthSubmitButton.tsx", [], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\forms\\FormDivider.tsx", [], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\forms\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\forms\\OAuthButton.tsx", [], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\forms\\RoleSelector.tsx", [], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\forms\\TermsCheckbox.tsx", [], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\layout\\AppLayout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\layout\\AuthLayout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\layout\\Breadcrumbs.tsx", [], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\layout\\PublicHeader.tsx", [], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\layout\\Sidebar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\providers\\MockAuthProvider.tsx", ["276"], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\providers\\NextAuthProvider.tsx", [], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\providers\\SessionSync.tsx", [], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\ui\\avatar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\ui\\dialog.tsx", [], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\ui\\dropdown-menu.tsx", [], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\ui\\form.tsx", [], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\ui\\select.tsx", [], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\ui\\sonner.tsx", [], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\ui\\textarea.tsx", [], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\hooks\\useApi.ts", ["277", "278", "279", "280", "281", "282", "283", "284", "285", "286", "287", "288", "289", "290", "291", "292", "293", "294"], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\hooks\\useForm.ts", ["295", "296", "297", "298", "299", "300", "301", "302", "303", "304", "305", "306", "307", "308", "309", "310", "311", "312", "313"], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\hooks\\useLocalStorage.ts", ["314"], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\hooks\\useRouteProtection.ts", [], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\lib\\auth.ts", ["315"], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\lib\\constants.ts", [], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\lib\\utils.ts", ["316", "317", "318", "319", "320", "321"], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\lib\\validations\\auth.ts", [], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\store\\authStore.ts", [], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\store\\uiStore.ts", [], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\types\\appointment.ts", [], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\types\\auth.ts", [], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\types\\common.ts", ["322", "323", "324", "325", "326", "327", "328", "329", "330"], [], "C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\types\\document.ts", ["331", "332", "333"], [], {"ruleId": "334", "severity": 1, "message": "335", "line": 55, "column": 23, "nodeType": null, "messageId": "336", "endLine": 55, "endColumn": 32, "suppressions": "337"}, {"ruleId": "334", "severity": 1, "message": "338", "line": 20, "column": 31, "nodeType": null, "messageId": "336", "endLine": 20, "endColumn": 47}, {"ruleId": "339", "severity": 1, "message": "340", "line": 48, "column": 33, "nodeType": "341", "messageId": "342", "endLine": 48, "endColumn": 36, "suggestions": "343"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 168, "column": 56, "nodeType": "341", "messageId": "342", "endLine": 168, "endColumn": 59, "suggestions": "344"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 47, "column": 21, "nodeType": "341", "messageId": "342", "endLine": 47, "endColumn": 24, "suggestions": "345"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 18, "column": 22, "nodeType": "341", "messageId": "342", "endLine": 18, "endColumn": 25, "suggestions": "346"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 26, "column": 22, "nodeType": "341", "messageId": "342", "endLine": 26, "endColumn": 25, "suggestions": "347"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 32, "column": 28, "nodeType": "341", "messageId": "342", "endLine": 32, "endColumn": 31, "suggestions": "348"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 58, "column": 32, "nodeType": "341", "messageId": "342", "endLine": 58, "endColumn": 35, "suggestions": "349"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 103, "column": 21, "nodeType": "341", "messageId": "342", "endLine": 103, "endColumn": 24, "suggestions": "350"}, {"ruleId": "351", "severity": 1, "message": "352", "line": 121, "column": 13, "nodeType": "353", "messageId": "354", "endLine": 121, "endColumn": 32, "fix": "355"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 243, "column": 28, "nodeType": "341", "messageId": "342", "endLine": 243, "endColumn": 31, "suggestions": "356"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 247, "column": 29, "nodeType": "341", "messageId": "342", "endLine": 247, "endColumn": 32, "suggestions": "357"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 251, "column": 12, "nodeType": "341", "messageId": "342", "endLine": 251, "endColumn": 15, "suggestions": "358"}, {"ruleId": "359", "severity": 1, "message": "360", "line": 257, "column": 5, "nodeType": "361", "endLine": 257, "endColumn": 18, "suggestions": "362"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 266, "column": 28, "nodeType": "341", "messageId": "342", "endLine": 266, "endColumn": 31, "suggestions": "363"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 270, "column": 12, "nodeType": "341", "messageId": "342", "endLine": 270, "endColumn": 15, "suggestions": "364"}, {"ruleId": "359", "severity": 1, "message": "360", "line": 276, "column": 5, "nodeType": "361", "endLine": 276, "endColumn": 18, "suggestions": "365"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 285, "column": 31, "nodeType": "341", "messageId": "342", "endLine": 285, "endColumn": 34, "suggestions": "366"}, {"ruleId": "334", "severity": 1, "message": "367", "line": 290, "column": 13, "nodeType": null, "messageId": "336", "endLine": 290, "endColumn": 16}, {"ruleId": "359", "severity": 1, "message": "360", "line": 295, "column": 5, "nodeType": "361", "endLine": 295, "endColumn": 28, "suggestions": "368"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 311, "column": 56, "nodeType": "341", "messageId": "342", "endLine": 311, "endColumn": 59, "suggestions": "369"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 311, "column": 71, "nodeType": "341", "messageId": "342", "endLine": 311, "endColumn": 74, "suggestions": "370"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 5, "column": 10, "nodeType": "341", "messageId": "342", "endLine": 5, "endColumn": 13, "suggestions": "371"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 23, "column": 20, "nodeType": "341", "messageId": "342", "endLine": 23, "endColumn": 23, "suggestions": "372"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 27, "column": 33, "nodeType": "341", "messageId": "342", "endLine": 27, "endColumn": 36, "suggestions": "373"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 29, "column": 38, "nodeType": "341", "messageId": "342", "endLine": 29, "endColumn": 41, "suggestions": "374"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 35, "column": 26, "nodeType": "341", "messageId": "342", "endLine": 35, "endColumn": 29, "suggestions": "375"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 44, "column": 35, "nodeType": "341", "messageId": "342", "endLine": 44, "endColumn": 38, "suggestions": "376"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 50, "column": 38, "nodeType": "341", "messageId": "342", "endLine": 50, "endColumn": 41, "suggestions": "377"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 52, "column": 38, "nodeType": "341", "messageId": "342", "endLine": 52, "endColumn": 41, "suggestions": "378"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 54, "column": 40, "nodeType": "341", "messageId": "342", "endLine": 54, "endColumn": 43, "suggestions": "379"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 57, "column": 43, "nodeType": "341", "messageId": "342", "endLine": 57, "endColumn": 46, "suggestions": "380"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 63, "column": 12, "nodeType": "341", "messageId": "342", "endLine": 63, "endColumn": 15, "suggestions": "381"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 65, "column": 23, "nodeType": "341", "messageId": "342", "endLine": 65, "endColumn": 26, "suggestions": "382"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 97, "column": 59, "nodeType": "341", "messageId": "342", "endLine": 97, "endColumn": 62, "suggestions": "383"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 184, "column": 54, "nodeType": "341", "messageId": "342", "endLine": 184, "endColumn": 57, "suggestions": "384"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 234, "column": 57, "nodeType": "341", "messageId": "342", "endLine": 234, "endColumn": 60, "suggestions": "385"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 265, "column": 57, "nodeType": "341", "messageId": "342", "endLine": 265, "endColumn": 60, "suggestions": "386"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 284, "column": 62, "nodeType": "341", "messageId": "342", "endLine": 284, "endColumn": 65, "suggestions": "387"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 305, "column": 31, "nodeType": "341", "messageId": "342", "endLine": 305, "endColumn": 34, "suggestions": "388"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 331, "column": 27, "nodeType": "341", "messageId": "342", "endLine": 331, "endColumn": 30, "suggestions": "389"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 134, "column": 66, "nodeType": "341", "messageId": "342", "endLine": 134, "endColumn": 69, "suggestions": "390"}, {"ruleId": "334", "severity": 1, "message": "391", "line": 91, "column": 35, "nodeType": null, "messageId": "336", "endLine": 91, "endColumn": 42}, {"ruleId": "334", "severity": 1, "message": "392", "line": 13, "column": 33, "nodeType": null, "messageId": "336", "endLine": 13, "endColumn": 42}, {"ruleId": "339", "severity": 1, "message": "340", "line": 254, "column": 29, "nodeType": "341", "messageId": "342", "endLine": 254, "endColumn": 32, "suggestions": "393"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 283, "column": 46, "nodeType": "341", "messageId": "342", "endLine": 283, "endColumn": 49, "suggestions": "394"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 283, "column": 56, "nodeType": "341", "messageId": "342", "endLine": 283, "endColumn": 59, "suggestions": "395"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 295, "column": 46, "nodeType": "341", "messageId": "342", "endLine": 295, "endColumn": 49, "suggestions": "396"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 295, "column": 56, "nodeType": "341", "messageId": "342", "endLine": 295, "endColumn": 59, "suggestions": "397"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 4, "column": 34, "nodeType": "341", "messageId": "342", "endLine": 4, "endColumn": 37, "suggestions": "398"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 48, "column": 28, "nodeType": "341", "messageId": "342", "endLine": 48, "endColumn": 31, "suggestions": "399"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 58, "column": 18, "nodeType": "341", "messageId": "342", "endLine": 58, "endColumn": 21, "suggestions": "400"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 75, "column": 11, "nodeType": "341", "messageId": "342", "endLine": 75, "endColumn": 14, "suggestions": "401"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 80, "column": 26, "nodeType": "341", "messageId": "342", "endLine": 80, "endColumn": 29, "suggestions": "402"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 99, "column": 33, "nodeType": "341", "messageId": "342", "endLine": 99, "endColumn": 36, "suggestions": "403"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 258, "column": 30, "nodeType": "341", "messageId": "342", "endLine": 258, "endColumn": 33, "suggestions": "404"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 259, "column": 30, "nodeType": "341", "messageId": "342", "endLine": 259, "endColumn": 33, "suggestions": "405"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 263, "column": 29, "nodeType": "341", "messageId": "342", "endLine": 263, "endColumn": 32, "suggestions": "406"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 87, "column": 18, "nodeType": "341", "messageId": "342", "endLine": 87, "endColumn": 21, "suggestions": "407"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 223, "column": 18, "nodeType": "341", "messageId": "342", "endLine": 223, "endColumn": 21, "suggestions": "408"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 237, "column": 29, "nodeType": "341", "messageId": "342", "endLine": 237, "endColumn": 32, "suggestions": "409"}, "@typescript-eslint/no-unused-vars", "'_password' is assigned a value but never used.", "unusedVar", ["410"], "'RegisterFormData' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["411", "412"], ["413", "414"], ["415", "416"], ["417", "418"], ["419", "420"], ["421", "422"], ["423", "424"], ["425", "426"], "prefer-const", "'config' is never reassigned. Use 'const' instead.", "Identifier", "useConst", {"range": "427", "text": "428"}, ["429", "430"], ["431", "432"], ["433", "434"], "react-hooks/exhaustive-deps", "React Hook useCallback has a missing dependency: 'api'. Either include it or remove the dependency array.", "ArrayExpression", ["435"], ["436", "437"], ["438", "439"], ["440"], ["441", "442"], "'url' is assigned a value but never used.", ["443"], ["444", "445"], ["446", "447"], ["448", "449"], ["450", "451"], ["452", "453"], ["454", "455"], ["456", "457"], ["458", "459"], ["460", "461"], ["462", "463"], ["464", "465"], ["466", "467"], ["468", "469"], ["470", "471"], ["472", "473"], ["474", "475"], ["476", "477"], ["478", "479"], ["480", "481"], ["482", "483"], ["484", "485"], ["486", "487"], "'profile' is defined but never used.", "'formatStr' is assigned a value but never used.", ["488", "489"], ["490", "491"], ["492", "493"], ["494", "495"], ["496", "497"], ["498", "499"], ["500", "501"], ["502", "503"], ["504", "505"], ["506", "507"], ["508", "509"], ["510", "511"], ["512", "513"], ["514", "515"], ["516", "517"], ["518", "519"], ["520", "521"], {"kind": "522", "justification": "523"}, {"messageId": "524", "fix": "525", "desc": "526"}, {"messageId": "527", "fix": "528", "desc": "529"}, {"messageId": "524", "fix": "530", "desc": "526"}, {"messageId": "527", "fix": "531", "desc": "529"}, {"messageId": "524", "fix": "532", "desc": "526"}, {"messageId": "527", "fix": "533", "desc": "529"}, {"messageId": "524", "fix": "534", "desc": "526"}, {"messageId": "527", "fix": "535", "desc": "529"}, {"messageId": "524", "fix": "536", "desc": "526"}, {"messageId": "527", "fix": "537", "desc": "529"}, {"messageId": "524", "fix": "538", "desc": "526"}, {"messageId": "527", "fix": "539", "desc": "529"}, {"messageId": "524", "fix": "540", "desc": "526"}, {"messageId": "527", "fix": "541", "desc": "529"}, {"messageId": "524", "fix": "542", "desc": "526"}, {"messageId": "527", "fix": "543", "desc": "529"}, [3207, 3280], "const config: RequestInit = {\n          headers: client.headers,\n        };", {"messageId": "524", "fix": "544", "desc": "526"}, {"messageId": "527", "fix": "545", "desc": "529"}, {"messageId": "524", "fix": "546", "desc": "526"}, {"messageId": "527", "fix": "547", "desc": "529"}, {"messageId": "524", "fix": "548", "desc": "526"}, {"messageId": "527", "fix": "549", "desc": "529"}, {"desc": "550", "fix": "551"}, {"messageId": "524", "fix": "552", "desc": "526"}, {"messageId": "527", "fix": "553", "desc": "529"}, {"messageId": "524", "fix": "554", "desc": "526"}, {"messageId": "527", "fix": "555", "desc": "529"}, {"desc": "550", "fix": "556"}, {"messageId": "524", "fix": "557", "desc": "526"}, {"messageId": "527", "fix": "558", "desc": "529"}, {"desc": "559", "fix": "560"}, {"messageId": "524", "fix": "561", "desc": "526"}, {"messageId": "527", "fix": "562", "desc": "529"}, {"messageId": "524", "fix": "563", "desc": "526"}, {"messageId": "527", "fix": "564", "desc": "529"}, {"messageId": "524", "fix": "565", "desc": "526"}, {"messageId": "527", "fix": "566", "desc": "529"}, {"messageId": "524", "fix": "567", "desc": "526"}, {"messageId": "527", "fix": "568", "desc": "529"}, {"messageId": "524", "fix": "569", "desc": "526"}, {"messageId": "527", "fix": "570", "desc": "529"}, {"messageId": "524", "fix": "571", "desc": "526"}, {"messageId": "527", "fix": "572", "desc": "529"}, {"messageId": "524", "fix": "573", "desc": "526"}, {"messageId": "527", "fix": "574", "desc": "529"}, {"messageId": "524", "fix": "575", "desc": "526"}, {"messageId": "527", "fix": "576", "desc": "529"}, {"messageId": "524", "fix": "577", "desc": "526"}, {"messageId": "527", "fix": "578", "desc": "529"}, {"messageId": "524", "fix": "579", "desc": "526"}, {"messageId": "527", "fix": "580", "desc": "529"}, {"messageId": "524", "fix": "581", "desc": "526"}, {"messageId": "527", "fix": "582", "desc": "529"}, {"messageId": "524", "fix": "583", "desc": "526"}, {"messageId": "527", "fix": "584", "desc": "529"}, {"messageId": "524", "fix": "585", "desc": "526"}, {"messageId": "527", "fix": "586", "desc": "529"}, {"messageId": "524", "fix": "587", "desc": "526"}, {"messageId": "527", "fix": "588", "desc": "529"}, {"messageId": "524", "fix": "589", "desc": "526"}, {"messageId": "527", "fix": "590", "desc": "529"}, {"messageId": "524", "fix": "591", "desc": "526"}, {"messageId": "527", "fix": "592", "desc": "529"}, {"messageId": "524", "fix": "593", "desc": "526"}, {"messageId": "527", "fix": "594", "desc": "529"}, {"messageId": "524", "fix": "595", "desc": "526"}, {"messageId": "527", "fix": "596", "desc": "529"}, {"messageId": "524", "fix": "597", "desc": "526"}, {"messageId": "527", "fix": "598", "desc": "529"}, {"messageId": "524", "fix": "599", "desc": "526"}, {"messageId": "527", "fix": "600", "desc": "529"}, {"messageId": "524", "fix": "601", "desc": "526"}, {"messageId": "527", "fix": "602", "desc": "529"}, {"messageId": "524", "fix": "603", "desc": "526"}, {"messageId": "527", "fix": "604", "desc": "529"}, {"messageId": "524", "fix": "605", "desc": "526"}, {"messageId": "527", "fix": "606", "desc": "529"}, {"messageId": "524", "fix": "607", "desc": "526"}, {"messageId": "527", "fix": "608", "desc": "529"}, {"messageId": "524", "fix": "609", "desc": "526"}, {"messageId": "527", "fix": "610", "desc": "529"}, {"messageId": "524", "fix": "611", "desc": "526"}, {"messageId": "527", "fix": "612", "desc": "529"}, {"messageId": "524", "fix": "613", "desc": "526"}, {"messageId": "527", "fix": "614", "desc": "529"}, {"messageId": "524", "fix": "615", "desc": "526"}, {"messageId": "527", "fix": "616", "desc": "529"}, {"messageId": "524", "fix": "617", "desc": "526"}, {"messageId": "527", "fix": "618", "desc": "529"}, {"messageId": "524", "fix": "619", "desc": "526"}, {"messageId": "527", "fix": "620", "desc": "529"}, {"messageId": "524", "fix": "621", "desc": "526"}, {"messageId": "527", "fix": "622", "desc": "529"}, {"messageId": "524", "fix": "623", "desc": "526"}, {"messageId": "527", "fix": "624", "desc": "529"}, {"messageId": "524", "fix": "625", "desc": "526"}, {"messageId": "527", "fix": "626", "desc": "529"}, {"messageId": "524", "fix": "627", "desc": "526"}, {"messageId": "527", "fix": "628", "desc": "529"}, {"messageId": "524", "fix": "629", "desc": "526"}, {"messageId": "527", "fix": "630", "desc": "529"}, {"messageId": "524", "fix": "631", "desc": "526"}, {"messageId": "527", "fix": "632", "desc": "529"}, {"messageId": "524", "fix": "633", "desc": "526"}, {"messageId": "527", "fix": "634", "desc": "529"}, {"messageId": "524", "fix": "635", "desc": "526"}, {"messageId": "527", "fix": "636", "desc": "529"}, {"messageId": "524", "fix": "637", "desc": "526"}, {"messageId": "527", "fix": "638", "desc": "529"}, "directive", "", "suggestUnknown", {"range": "639", "text": "640"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "641", "text": "642"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "643", "text": "640"}, {"range": "644", "text": "642"}, {"range": "645", "text": "640"}, {"range": "646", "text": "642"}, {"range": "647", "text": "640"}, {"range": "648", "text": "642"}, {"range": "649", "text": "640"}, {"range": "650", "text": "642"}, {"range": "651", "text": "640"}, {"range": "652", "text": "642"}, {"range": "653", "text": "640"}, {"range": "654", "text": "642"}, {"range": "655", "text": "640"}, {"range": "656", "text": "642"}, {"range": "657", "text": "640"}, {"range": "658", "text": "642"}, {"range": "659", "text": "640"}, {"range": "660", "text": "642"}, {"range": "661", "text": "640"}, {"range": "662", "text": "642"}, "Update the dependencies array to be: [api]", {"range": "663", "text": "664"}, {"range": "665", "text": "640"}, {"range": "666", "text": "642"}, {"range": "667", "text": "640"}, {"range": "668", "text": "642"}, {"range": "669", "text": "664"}, {"range": "670", "text": "640"}, {"range": "671", "text": "642"}, "Update the dependencies array to be: [api, endpoint]", {"range": "672", "text": "673"}, {"range": "674", "text": "640"}, {"range": "675", "text": "642"}, {"range": "676", "text": "640"}, {"range": "677", "text": "642"}, {"range": "678", "text": "640"}, {"range": "679", "text": "642"}, {"range": "680", "text": "640"}, {"range": "681", "text": "642"}, {"range": "682", "text": "640"}, {"range": "683", "text": "642"}, {"range": "684", "text": "640"}, {"range": "685", "text": "642"}, {"range": "686", "text": "640"}, {"range": "687", "text": "642"}, {"range": "688", "text": "640"}, {"range": "689", "text": "642"}, {"range": "690", "text": "640"}, {"range": "691", "text": "642"}, {"range": "692", "text": "640"}, {"range": "693", "text": "642"}, {"range": "694", "text": "640"}, {"range": "695", "text": "642"}, {"range": "696", "text": "640"}, {"range": "697", "text": "642"}, {"range": "698", "text": "640"}, {"range": "699", "text": "642"}, {"range": "700", "text": "640"}, {"range": "701", "text": "642"}, {"range": "702", "text": "640"}, {"range": "703", "text": "642"}, {"range": "704", "text": "640"}, {"range": "705", "text": "642"}, {"range": "706", "text": "640"}, {"range": "707", "text": "642"}, {"range": "708", "text": "640"}, {"range": "709", "text": "642"}, {"range": "710", "text": "640"}, {"range": "711", "text": "642"}, {"range": "712", "text": "640"}, {"range": "713", "text": "642"}, {"range": "714", "text": "640"}, {"range": "715", "text": "642"}, {"range": "716", "text": "640"}, {"range": "717", "text": "642"}, {"range": "718", "text": "640"}, {"range": "719", "text": "642"}, {"range": "720", "text": "640"}, {"range": "721", "text": "642"}, {"range": "722", "text": "640"}, {"range": "723", "text": "642"}, {"range": "724", "text": "640"}, {"range": "725", "text": "642"}, {"range": "726", "text": "640"}, {"range": "727", "text": "642"}, {"range": "728", "text": "640"}, {"range": "729", "text": "642"}, {"range": "730", "text": "640"}, {"range": "731", "text": "642"}, {"range": "732", "text": "640"}, {"range": "733", "text": "642"}, {"range": "734", "text": "640"}, {"range": "735", "text": "642"}, {"range": "736", "text": "640"}, {"range": "737", "text": "642"}, {"range": "738", "text": "640"}, {"range": "739", "text": "642"}, {"range": "740", "text": "640"}, {"range": "741", "text": "642"}, {"range": "742", "text": "640"}, {"range": "743", "text": "642"}, {"range": "744", "text": "640"}, {"range": "745", "text": "642"}, {"range": "746", "text": "640"}, {"range": "747", "text": "642"}, {"range": "748", "text": "640"}, {"range": "749", "text": "642"}, {"range": "750", "text": "640"}, {"range": "751", "text": "642"}, [1174, 1177], "unknown", [1174, 1177], "never", [4792, 4795], [4792, 4795], [1515, 1518], [1515, 1518], [491, 494], [491, 494], [720, 723], [720, 723], [854, 857], [854, 857], [1491, 1494], [1491, 1494], [2746, 2749], [2746, 2749], [6084, 6087], [6084, 6087], [6229, 6232], [6229, 6232], [6366, 6369], [6366, 6369], [6468, 6481], "[api]", [6556, 6559], [6556, 6559], [6692, 6695], [6692, 6695], [6793, 6806], [6883, 6886], [6883, 6886], [7169, 7192], "[api, endpoint]", [7588, 7591], [7588, 7591], [7603, 7606], [7603, 7606], [141, 144], [141, 144], [470, 473], [470, 473], [558, 561], [558, 561], [653, 656], [653, 656], [803, 806], [803, 806], [1044, 1047], [1044, 1047], [1302, 1305], [1302, 1305], [1416, 1419], [1416, 1419], [1515, 1518], [1515, 1518], [1603, 1606], [1603, 1606], [1774, 1777], [1774, 1777], [1827, 1830], [1827, 1830], [2608, 2611], [2608, 2611], [4924, 4927], [4924, 4927], [6323, 6326], [6323, 6326], [7232, 7235], [7232, 7235], [7733, 7736], [7733, 7736], [8278, 8281], [8278, 8281], [9017, 9020], [9017, 9020], [4170, 4173], [4170, 4173], [7071, 7074], [7071, 7074], [7968, 7971], [7968, 7971], [7978, 7981], [7978, 7981], [8258, 8261], [8258, 8261], [8268, 8271], [8268, 8271], [95, 98], [95, 98], [906, 909], [906, 909], [1169, 1172], [1169, 1172], [1535, 1538], [1535, 1538], [1616, 1619], [1616, 1619], [1986, 1989], [1986, 1989], [5267, 5270], [5267, 5270], [5302, 5305], [5302, 5305], [5397, 5400], [5397, 5400], [2092, 2095], [2092, 2095], [5097, 5100], [5097, 5100], [5386, 5389], [5386, 5389]]