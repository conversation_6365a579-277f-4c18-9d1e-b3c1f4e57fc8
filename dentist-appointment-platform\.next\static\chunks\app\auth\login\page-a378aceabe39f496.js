(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[859],{4177:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>p});var a=r(5155),o=r(2115),i=r(5695),t=r(6874),l=r.n(t),n=r(2177),d=r(8778),c=r(6671),u=r(2108),m=r(1929),h=r(6434),g=r(742);let p=()=>{var e,s;let r=(0,i.useRouter)(),[t,p]=(0,o.useState)(!1),[x,y]=(0,o.useState)(!1),{handleSubmit:f,formState:{errors:v},setValue:j,watch:w}=(0,n.mN)({resolver:(0,d.u)(g.X5),defaultValues:{email:"",password:""}}),b=w(),N=async e=>{try{p(!0);let s=await (0,u.signIn)("credentials",{email:e.email,password:e.password,redirect:!1});(null==s?void 0:s.error)?c.oR.error("Login failed",{description:"Please check your credentials and try again."}):(c.oR.success("Login successful!",{description:"Welcome back to DentCare Pro."}),r.push("/dashboard"))}catch(e){c.oR.error("Login failed",{description:e instanceof Error?e.message:"Please check your credentials and try again."})}finally{p(!1)}},k=async()=>{try{y(!0);let e=await (0,u.signIn)("google",{callbackUrl:"/dashboard",redirect:!1});(null==e?void 0:e.error)?c.oR.error("OAuth login failed",{description:"Please try again or use email/password login."}):(null==e?void 0:e.url)&&(c.oR.success("Login successful!",{description:"Redirecting to dashboard..."}),r.push(e.url))}catch(e){console.error("OAuth login error:",e),c.oR.error("OAuth login failed",{description:"Please try again or use email/password login."})}finally{y(!1)}};return(0,a.jsx)(m.A,{title:"Welcome back",subtitle:"Sign in to your DentCare Pro account",children:(0,a.jsxs)("form",{onSubmit:f(N),className:"space-y-6",children:[(0,a.jsx)(h.vy,{label:"Email address",type:"email",placeholder:"Enter your email",value:b.email,onChange:e=>j("email",e),error:null==(e=v.email)?void 0:e.message,required:!0,autoComplete:"email"}),(0,a.jsx)(h.vy,{label:"Password",type:"password",placeholder:"Enter your password",value:b.password,onChange:e=>j("password",e),error:null==(s=v.password)?void 0:s.message,required:!0,autoComplete:"current-password"}),(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsx)(l(),{href:"/auth/forgot-password",className:"text-sm text-primary hover:text-primary/80 transition-colors",children:"Forgot your password?"})}),(0,a.jsx)(h.FX,{isLoading:t,disabled:t||x,loadingText:"Signing in...",children:"Sign in"}),(0,a.jsx)(h.Sq,{}),(0,a.jsx)(h.ID,{provider:"google",isLoading:x,disabled:t||x,onClick:k}),(0,a.jsxs)("div",{className:"p-4 bg-muted/50 rounded-lg",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-foreground mb-2",children:"Test Credentials"}),(0,a.jsxs)("div",{className:"text-xs text-muted-foreground space-y-1",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"Patient:"})," <EMAIL> / password123"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"Dentist:"})," <EMAIL> / password123"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"Admin:"})," <EMAIL> / password123"]})]})]}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Don't have an account?"," ",(0,a.jsx)(l(),{href:"/auth/register",className:"text-primary hover:text-primary/80 transition-colors font-medium",children:"Sign up"})]})})]})})}},5619:(e,s,r)=>{Promise.resolve().then(r.bind(r,4177))}},e=>{var s=s=>e(e.s=s);e.O(0,[52,919,3,981,571,441,684,358],()=>s(5619)),_N_E=e.O()}]);