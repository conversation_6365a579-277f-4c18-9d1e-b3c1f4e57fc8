"use strict";exports.id=383,exports.ids=[383],exports.modules={13977:(e,s,a)=>{a.d(s,{vy:()=>m,FX:()=>p,Sq:()=>f,ID:()=>h,QT:()=>j,PD:()=>P});var r=a(60687),t=a(43210),i=a(12597),l=a(13861),n=a(60140);function d({className:e,type:s,...a}){return(0,r.jsx)("input",{type:s,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...a})}var o=a(78148);function c({className:e,...s}){return(0,r.jsx)(o.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...s})}let m=({label:e,type:s="text",placeholder:a,value:o,onChange:m,error:x,required:u=!1,disabled:p=!1,className:h,autoComplete:f,id:b})=>{let[g,v]=(0,t.useState)(!1),j=b||`field-${e.toLowerCase().replace(/\s+/g,"-")}`,N="password"===s,y=N&&g?"text":s;return(0,r.jsxs)("div",{className:(0,n.cn)("space-y-2",h),children:[(0,r.jsxs)(c,{htmlFor:j,className:(0,n.cn)("text-sm font-medium text-foreground",x&&"text-destructive"),children:[e,u&&(0,r.jsx)("span",{className:"text-destructive ml-1",children:"*"})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(d,{id:j,type:y,placeholder:a,value:o,onChange:e=>m(e.target.value),disabled:p,autoComplete:f,className:(0,n.cn)("glass-input w-full",x&&"border-destructive focus:border-destructive",N&&"pr-10"),"aria-invalid":!!x,"aria-describedby":x?`${j}-error`:void 0}),N&&(0,r.jsx)("button",{type:"button",onClick:()=>v(!g),className:"absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors",tabIndex:-1,"aria-label":g?"Hide password":"Show password",children:g?(0,r.jsx)(i.A,{className:"w-4 h-4"}):(0,r.jsx)(l.A,{className:"w-4 h-4"})})]}),x&&(0,r.jsx)("p",{id:`${j}-error`,className:"text-sm text-destructive",role:"alert",children:x})]})};var x=a(41862),u=a(29523);let p=({children:e,isLoading:s=!1,disabled:a=!1,type:t="submit",variant:i="default",size:l="default",className:d,onClick:o,loadingText:c="Please wait..."})=>{let m=a||s;return(0,r.jsxs)(u.$,{type:t,variant:i,size:l,disabled:m,onClick:o,className:(0,n.cn)("glass-button w-full relative","transition-all duration-300 ease-in-out","disabled:opacity-50 disabled:cursor-not-allowed",d),children:[s&&(0,r.jsx)(x.A,{className:"w-4 h-4 mr-2 animate-spin"}),s?c:e]})},h=({provider:e,isLoading:s=!1,disabled:a=!1,onClick:t,className:i,children:l})=>(0,r.jsxs)(u.$,{type:"button",variant:"outline",disabled:a||s,onClick:t,className:(0,n.cn)("w-full glass-input border-border/40 hover:bg-accent/50","transition-all duration-300 ease-in-out","disabled:opacity-50 disabled:cursor-not-allowed",i),children:[s?(0,r.jsx)(x.A,{className:"w-5 h-5 mr-3 animate-spin"}):(0,r.jsx)("span",{className:"mr-3",children:(()=>{switch(e){case"google":return(0,r.jsxs)("svg",{className:"w-5 h-5",viewBox:"0 0 24 24",children:[(0,r.jsx)("path",{fill:"#4285F4",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,r.jsx)("path",{fill:"#34A853",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,r.jsx)("path",{fill:"#FBBC05",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,r.jsx)("path",{fill:"#EA4335",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]});case"microsoft":return(0,r.jsxs)("svg",{className:"w-5 h-5",viewBox:"0 0 24 24",children:[(0,r.jsx)("path",{fill:"#F25022",d:"M1 1h10v10H1z"}),(0,r.jsx)("path",{fill:"#00A4EF",d:"M13 1h10v10H13z"}),(0,r.jsx)("path",{fill:"#7FBA00",d:"M1 13h10v10H1z"}),(0,r.jsx)("path",{fill:"#FFB900",d:"M13 13h10v10H13z"})]});case"apple":return(0,r.jsx)("svg",{className:"w-5 h-5",viewBox:"0 0 24 24",fill:"currentColor",children:(0,r.jsx)("path",{d:"M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"})});default:return null}})()}),l||`Continue with ${(()=>{switch(e){case"google":return"Google";case"microsoft":return"Microsoft";case"apple":return"Apple";default:return e}})()}`]}),f=({text:e="or",className:s})=>(0,r.jsxs)("div",{className:(0,n.cn)("relative flex items-center",s),children:[(0,r.jsx)("div",{className:"flex-1 border-t border-border/40"}),(0,r.jsx)("div",{className:"px-4",children:(0,r.jsx)("span",{className:"text-sm text-muted-foreground bg-background px-2",children:e})}),(0,r.jsx)("div",{className:"flex-1 border-t border-border/40"})]});var b=a(53881),g=a(58869),v=a(60137);let j=({value:e,onChange:s,error:a,disabled:t=!1,className:i})=>{let l=[{value:b.g.PATIENT,label:"Patient",description:"Book appointments and manage your dental care",icon:g.A},{value:b.g.DENTIST,label:"Dentist",description:"Manage practice, patients, and appointments",icon:v.A}];return(0,r.jsxs)("div",{className:(0,n.cn)("space-y-3",i),children:[(0,r.jsxs)("label",{className:"text-sm font-medium text-foreground",children:["I am a ",(0,r.jsx)("span",{className:"text-destructive",children:"*"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3",children:l.map(i=>{let l=i.icon,d=e===i.value;return(0,r.jsx)("button",{type:"button",onClick:()=>!t&&s(i.value),disabled:t,className:(0,n.cn)("p-4 rounded-lg border-2 transition-all duration-200","text-left hover:bg-accent/50 focus:outline-none focus:ring-2 focus:ring-primary/50","disabled:opacity-50 disabled:cursor-not-allowed",d?"border-primary bg-primary/10 text-foreground":"border-border/40 bg-background/50 text-muted-foreground hover:text-foreground",a&&"border-destructive"),children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:(0,n.cn)("w-8 h-8 rounded-lg flex items-center justify-center",d?"bg-primary/20 text-primary":"bg-muted text-muted-foreground"),children:(0,r.jsx)(l,{className:"w-4 h-4"})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("h3",{className:(0,n.cn)("font-medium text-sm","text-foreground"),children:i.label}),(0,r.jsx)("p",{className:(0,n.cn)("text-xs mt-1","text-muted-foreground"),children:i.description})]})]})},i.value)})}),a&&(0,r.jsx)("p",{className:"text-sm text-destructive",role:"alert",children:a})]})};var N=a(85814),y=a.n(N),w=a(13964);let P=({checked:e,onChange:s,error:a,disabled:t=!1,className:i})=>(0,r.jsxs)("div",{className:(0,n.cn)("space-y-2",i),children:[(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("button",{type:"button",onClick:()=>!t&&s(!e),disabled:t,className:(0,n.cn)("w-5 h-5 rounded border-2 flex items-center justify-center transition-all duration-200","focus:outline-none focus:ring-2 focus:ring-primary/50","disabled:opacity-50 disabled:cursor-not-allowed",e?"bg-primary border-primary text-primary-foreground":"border-border/40 bg-background hover:border-primary/50",a&&"border-destructive"),"aria-checked":e,role:"checkbox",children:e&&(0,r.jsx)(w.A,{className:"w-3 h-3"})}),(0,r.jsx)("div",{className:"flex-1 text-sm",children:(0,r.jsxs)("label",{className:(0,n.cn)("text-muted-foreground leading-relaxed cursor-pointer",t&&"cursor-not-allowed"),children:["I agree to the"," ",(0,r.jsx)(y(),{href:"/legal/terms",className:"text-primary hover:text-primary/80 underline",target:"_blank",rel:"noopener noreferrer",children:"Terms of Service"})," ","and"," ",(0,r.jsx)(y(),{href:"/legal/privacy",className:"text-primary hover:text-primary/80 underline",target:"_blank",rel:"noopener noreferrer",children:"Privacy Policy"})," ",(0,r.jsx)("span",{className:"text-destructive",children:"*"})]})})]}),a&&(0,r.jsx)("p",{className:"text-sm text-destructive ml-8",role:"alert",children:a})]})},31923:(e,s,a)=>{a.d(s,{A:()=>m});var r=a(60687);a(43210);var t=a(85814),i=a.n(t),l=a(21121),n=a(67760),d=a(99891),o=a(41312),c=a(28559);let m=({children:e,title:s,subtitle:a,showBackButton:t=!0,backHref:m="/"})=>(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-background via-background to-secondary/20 flex flex-col lg:flex-row",children:[(0,r.jsxs)("div",{className:"hidden lg:flex lg:w-1/2 xl:w-2/5 flex-col justify-center p-12 relative overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-primary/10 via-transparent to-accent/10"}),(0,r.jsx)("div",{className:"absolute top-1/4 left-1/4 w-64 h-64 bg-primary/20 rounded-full blur-3xl"}),(0,r.jsx)("div",{className:"absolute bottom-1/4 right-1/4 w-48 h-48 bg-accent/20 rounded-full blur-3xl"}),(0,r.jsxs)("div",{className:"relative z-10",children:[(0,r.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},className:"mb-12",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3 mb-6",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-primary/20 rounded-xl flex items-center justify-center backdrop-blur-sm border border-primary/30",children:(0,r.jsx)(n.A,{className:"w-6 h-6 text-primary"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-foreground font-poppins",children:"DentCare Pro"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Professional Dental Management"})]})]})}),(0,r.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"space-y-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-3xl font-bold text-foreground mb-4 font-poppins",children:"Secure & Compliant"}),(0,r.jsx)("p",{className:"text-lg text-muted-foreground leading-relaxed",children:"HIPAA-compliant platform designed specifically for dental practices. Manage appointments, patient records, and documents with confidence."})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center",children:(0,r.jsx)(d.A,{className:"w-5 h-5 text-green-400"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-foreground mb-1",children:"Enterprise Security"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"End-to-end encryption and secure data handling"})]})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center",children:(0,r.jsx)(o.A,{className:"w-5 h-5 text-blue-400"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-foreground mb-1",children:"Multi-Role Access"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Tailored interfaces for patients, dentists, and staff"})]})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center",children:(0,r.jsx)(n.A,{className:"w-5 h-5 text-purple-400"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-foreground mb-1",children:"Patient-Centered"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Streamlined experience for better patient care"})]})]})]})]})]})]}),(0,r.jsx)("div",{className:"flex-1 flex flex-col justify-center p-6 sm:p-12 lg:p-16",children:(0,r.jsxs)("div",{className:"w-full max-w-md mx-auto",children:[t&&(0,r.jsx)(l.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.4},className:"mb-8",children:(0,r.jsxs)(i(),{href:m,className:"inline-flex items-center space-x-2 text-muted-foreground hover:text-foreground transition-colors",children:[(0,r.jsx)(c.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{className:"text-sm",children:"Back to home"})]})}),(0,r.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.1},className:"glass-card p-8 sm:p-10",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-foreground mb-2 font-poppins",children:s}),(0,r.jsx)("p",{className:"text-muted-foreground",children:a})]}),e]}),(0,r.jsx)(l.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:.6,delay:.3},className:"lg:hidden mt-8 text-center",children:(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"\xa9 2024 DentCare Pro. HIPAA-compliant dental practice management."})})]})})]})},35810:(e,s,a)=>{a.d(s,{X5:()=>t,zK:()=>i});var r=a(9275);let t=r.z.object({email:r.z.string().min(1,"Email is required").email("Please enter a valid email address"),password:r.z.string().min(1,"Password is required").min(8,"Password must be at least 8 characters long")}),i=r.z.object({firstName:r.z.string().min(1,"First name is required").min(2,"First name must be at least 2 characters").max(50,"First name must be less than 50 characters"),lastName:r.z.string().min(1,"Last name is required").min(2,"Last name must be at least 2 characters").max(50,"Last name must be less than 50 characters"),email:r.z.string().min(1,"Email is required").email("Please enter a valid email address"),password:r.z.string().min(8,"Password must be at least 8 characters long").regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,"Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character"),confirmPassword:r.z.string().min(1,"Please confirm your password"),role:r.z.union([r.z.enum(["PATIENT","DENTIST"]),r.z.literal("")],{required_error:"Please select a role"}).refine(e=>""!==e,{message:"Please select a role"}),phone:r.z.string().optional().refine(e=>!e||/^\+?[\d\s\-\(\)]+$/.test(e),{message:"Please enter a valid phone number"}),acceptTerms:r.z.boolean().refine(e=>!0===e,{message:"You must accept the terms and conditions"})}).refine(e=>e.password===e.confirmPassword,{message:"Passwords do not match",path:["confirmPassword"]});r.z.object({email:r.z.string().min(1,"Email is required").email("Please enter a valid email address")}),r.z.object({password:r.z.string().min(8,"Password must be at least 8 characters long").regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,"Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character"),confirmPassword:r.z.string().min(1,"Please confirm your password")}).refine(e=>e.password===e.confirmPassword,{message:"Passwords do not match",path:["confirmPassword"]})},70440:(e,s,a)=>{a.r(s),a.d(s,{default:()=>t});var r=a(31658);let t=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]}};