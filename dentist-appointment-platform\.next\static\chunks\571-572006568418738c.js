"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[571],{285:(e,t,r)=>{r.d(t,{$:()=>o});var s=r(5155);r(2115);var a=r(9708),i=r(2085),n=r(2911);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:r,size:i,asChild:o=!1,...d}=e,c=o?a.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,n.cn)(l({variant:r,size:i,className:t})),...d})}},742:(e,t,r)=>{r.d(t,{X5:()=>a,zK:()=>i});var s=r(1153);let a=s.z.object({email:s.z.string().min(1,"Email is required").email("Please enter a valid email address"),password:s.z.string().min(1,"Password is required").min(8,"Password must be at least 8 characters long")}),i=s.z.object({firstName:s.z.string().min(1,"First name is required").min(2,"First name must be at least 2 characters").max(50,"First name must be less than 50 characters"),lastName:s.z.string().min(1,"Last name is required").min(2,"Last name must be at least 2 characters").max(50,"Last name must be less than 50 characters"),email:s.z.string().min(1,"Email is required").email("Please enter a valid email address"),password:s.z.string().min(8,"Password must be at least 8 characters long").regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,"Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character"),confirmPassword:s.z.string().min(1,"Please confirm your password"),role:s.z.union([s.z.enum(["PATIENT","DENTIST"]),s.z.literal("")],{required_error:"Please select a role"}).refine(e=>""!==e,{message:"Please select a role"}),phone:s.z.string().optional().refine(e=>!e||/^\+?[\d\s\-\(\)]+$/.test(e),{message:"Please enter a valid phone number"}),acceptTerms:s.z.boolean().refine(e=>!0===e,{message:"You must accept the terms and conditions"})}).refine(e=>e.password===e.confirmPassword,{message:"Passwords do not match",path:["confirmPassword"]});s.z.object({email:s.z.string().min(1,"Email is required").email("Please enter a valid email address")}),s.z.object({password:s.z.string().min(8,"Password must be at least 8 characters long").regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,"Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character"),confirmPassword:s.z.string().min(1,"Please confirm your password")}).refine(e=>e.password===e.confirmPassword,{message:"Passwords do not match",path:["confirmPassword"]})},1929:(e,t,r)=>{r.d(t,{A:()=>m});var s=r(5155);r(2115);var a=r(6874),i=r.n(a),n=r(1934),l=r(1976),o=r(5525),d=r(7580),c=r(7550);let m=e=>{let{children:t,title:r,subtitle:a,showBackButton:m=!0,backHref:u="/"}=e;return(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-background via-background to-secondary/20 flex flex-col lg:flex-row",children:[(0,s.jsxs)("div",{className:"hidden lg:flex lg:w-1/2 xl:w-2/5 flex-col justify-center p-12 relative overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-primary/10 via-transparent to-accent/10"}),(0,s.jsx)("div",{className:"absolute top-1/4 left-1/4 w-64 h-64 bg-primary/20 rounded-full blur-3xl"}),(0,s.jsx)("div",{className:"absolute bottom-1/4 right-1/4 w-48 h-48 bg-accent/20 rounded-full blur-3xl"}),(0,s.jsxs)("div",{className:"relative z-10",children:[(0,s.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},className:"mb-12",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3 mb-6",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-primary/20 rounded-xl flex items-center justify-center backdrop-blur-sm border border-primary/30",children:(0,s.jsx)(l.A,{className:"w-6 h-6 text-primary"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-foreground font-poppins",children:"DentCare Pro"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Professional Dental Management"})]})]})}),(0,s.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"space-y-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-foreground mb-4 font-poppins",children:"Secure & Compliant"}),(0,s.jsx)("p",{className:"text-lg text-muted-foreground leading-relaxed",children:"HIPAA-compliant platform designed specifically for dental practices. Manage appointments, patient records, and documents with confidence."})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center",children:(0,s.jsx)(o.A,{className:"w-5 h-5 text-green-400"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-semibold text-foreground mb-1",children:"Enterprise Security"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"End-to-end encryption and secure data handling"})]})]}),(0,s.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center",children:(0,s.jsx)(d.A,{className:"w-5 h-5 text-blue-400"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-semibold text-foreground mb-1",children:"Multi-Role Access"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Tailored interfaces for patients, dentists, and staff"})]})]}),(0,s.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center",children:(0,s.jsx)(l.A,{className:"w-5 h-5 text-purple-400"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-semibold text-foreground mb-1",children:"Patient-Centered"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Streamlined experience for better patient care"})]})]})]})]})]})]}),(0,s.jsx)("div",{className:"flex-1 flex flex-col justify-center p-6 sm:p-12 lg:p-16",children:(0,s.jsxs)("div",{className:"w-full max-w-md mx-auto",children:[m&&(0,s.jsx)(n.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.4},className:"mb-8",children:(0,s.jsxs)(i(),{href:u,className:"inline-flex items-center space-x-2 text-muted-foreground hover:text-foreground transition-colors",children:[(0,s.jsx)(c.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{className:"text-sm",children:"Back to home"})]})}),(0,s.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.1},className:"glass-card p-8 sm:p-10",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-foreground mb-2 font-poppins",children:r}),(0,s.jsx)("p",{className:"text-muted-foreground",children:a})]}),t]}),(0,s.jsx)(n.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:.6,delay:.3},className:"lg:hidden mt-8 text-center",children:(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"\xa9 2024 DentCare Pro. HIPAA-compliant dental practice management."})})]})})]})}},2911:(e,t,r)=>{r.d(t,{cn:()=>n,DR:()=>l});var s=r(2596),a=r(9688);r(9509).env.NEXT_PUBLIC_API_URL,Array.from({length:48},(e,t)=>{let r=Math.floor(t/2),s=t%2==0?"00":"30",a="".concat(r.toString().padStart(2,"0"),":").concat(s),i=0===r?12:r>12?r-12:r,n=r<12?"AM":"PM";return{value:a,label:"".concat(i,":").concat(s," ").concat(n)}});let i={PASSWORD_MIN_LENGTH:8,NAME_MIN_LENGTH:2,NAME_MAX_LENGTH:50,DESCRIPTION_MAX_LENGTH:1e3,NOTES_MAX_LENGTH:2e3};function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,s.$)(t))}i.PASSWORD_MIN_LENGTH,i.NAME_MIN_LENGTH,i.NAME_MAX_LENGTH,i.DESCRIPTION_MAX_LENGTH,i.NOTES_MAX_LENGTH;let l={capitalize:e=>e.charAt(0).toUpperCase()+e.slice(1).toLowerCase(),capitalizeWords:e=>e.replace(/\w\S*/g,e=>e.charAt(0).toUpperCase()+e.substr(1).toLowerCase()),truncate:function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"...";return e.length<=t?e:e.substring(0,t)+r},slugify:e=>e.toLowerCase().replace(/[^\w\s-]/g,"").replace(/[\s_-]+/g,"-").replace(/^-+|-+$/g,""),initials:e=>e.split(" ").map(e=>e.charAt(0)).join("").toUpperCase().substring(0,2),formatPhoneNumber:e=>{let t=e.replace(/\D/g,"");return 10===t.length?"(".concat(t.slice(0,3),") ").concat(t.slice(3,6),"-").concat(t.slice(6)):e},maskEmail:e=>{let[t,r]=e.split("@");if(t.length<=2)return e;let s=t.charAt(0)+"*".repeat(t.length-2)+t.charAt(t.length-1);return"".concat(s,"@").concat(r)}}},6434:(e,t,r)=>{r.d(t,{vy:()=>m,FX:()=>p,Sq:()=>g,ID:()=>h,QT:()=>N,PD:()=>A});var s=r(5155),a=r(2115),i=r(8749),n=r(2657),l=r(2911);function o(e){let{className:t,type:r,...a}=e;return(0,s.jsx)("input",{type:r,"data-slot":"input",className:(0,l.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...a})}var d=r(968);function c(e){let{className:t,...r}=e;return(0,s.jsx)(d.b,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...r})}let m=e=>{let{label:t,type:r="text",placeholder:d,value:m,onChange:u,error:x,required:p=!1,disabled:h=!1,className:g,autoComplete:f,id:b}=e,[v,N]=(0,a.useState)(!1),j=b||"field-".concat(t.toLowerCase().replace(/\s+/g,"-")),y="password"===r,w=y&&v?"text":r;return(0,s.jsxs)("div",{className:(0,l.cn)("space-y-2",g),children:[(0,s.jsxs)(c,{htmlFor:j,className:(0,l.cn)("text-sm font-medium text-foreground",x&&"text-destructive"),children:[t,p&&(0,s.jsx)("span",{className:"text-destructive ml-1",children:"*"})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(o,{id:j,type:w,placeholder:d,value:m,onChange:e=>u(e.target.value),disabled:h,autoComplete:f,className:(0,l.cn)("glass-input w-full",x&&"border-destructive focus:border-destructive",y&&"pr-10"),"aria-invalid":!!x,"aria-describedby":x?"".concat(j,"-error"):void 0}),y&&(0,s.jsx)("button",{type:"button",onClick:()=>N(!v),className:"absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors",tabIndex:-1,"aria-label":v?"Hide password":"Show password",children:v?(0,s.jsx)(i.A,{className:"w-4 h-4"}):(0,s.jsx)(n.A,{className:"w-4 h-4"})})]}),x&&(0,s.jsx)("p",{id:"".concat(j,"-error"),className:"text-sm text-destructive",role:"alert",children:x})]})};var u=r(1154),x=r(285);let p=e=>{let{children:t,isLoading:r=!1,disabled:a=!1,type:i="submit",variant:n="default",size:o="default",className:d,onClick:c,loadingText:m="Please wait..."}=e,p=a||r;return(0,s.jsxs)(x.$,{type:i,variant:n,size:o,disabled:p,onClick:c,className:(0,l.cn)("glass-button w-full relative","transition-all duration-300 ease-in-out","disabled:opacity-50 disabled:cursor-not-allowed",d),children:[r&&(0,s.jsx)(u.A,{className:"w-4 h-4 mr-2 animate-spin"}),r?m:t]})},h=e=>{let{provider:t,isLoading:r=!1,disabled:a=!1,onClick:i,className:n,children:o}=e;return(0,s.jsxs)(x.$,{type:"button",variant:"outline",disabled:a||r,onClick:i,className:(0,l.cn)("w-full glass-input border-border/40 hover:bg-accent/50","transition-all duration-300 ease-in-out","disabled:opacity-50 disabled:cursor-not-allowed",n),children:[r?(0,s.jsx)(u.A,{className:"w-5 h-5 mr-3 animate-spin"}):(0,s.jsx)("span",{className:"mr-3",children:(()=>{switch(t){case"google":return(0,s.jsxs)("svg",{className:"w-5 h-5",viewBox:"0 0 24 24",children:[(0,s.jsx)("path",{fill:"#4285F4",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,s.jsx)("path",{fill:"#34A853",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,s.jsx)("path",{fill:"#FBBC05",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,s.jsx)("path",{fill:"#EA4335",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]});case"microsoft":return(0,s.jsxs)("svg",{className:"w-5 h-5",viewBox:"0 0 24 24",children:[(0,s.jsx)("path",{fill:"#F25022",d:"M1 1h10v10H1z"}),(0,s.jsx)("path",{fill:"#00A4EF",d:"M13 1h10v10H13z"}),(0,s.jsx)("path",{fill:"#7FBA00",d:"M1 13h10v10H1z"}),(0,s.jsx)("path",{fill:"#FFB900",d:"M13 13h10v10H13z"})]});case"apple":return(0,s.jsx)("svg",{className:"w-5 h-5",viewBox:"0 0 24 24",fill:"currentColor",children:(0,s.jsx)("path",{d:"M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"})});default:return null}})()}),o||"Continue with ".concat((()=>{switch(t){case"google":return"Google";case"microsoft":return"Microsoft";case"apple":return"Apple";default:return t}})())]})},g=e=>{let{text:t="or",className:r}=e;return(0,s.jsxs)("div",{className:(0,l.cn)("relative flex items-center",r),children:[(0,s.jsx)("div",{className:"flex-1 border-t border-border/40"}),(0,s.jsx)("div",{className:"px-4",children:(0,s.jsx)("span",{className:"text-sm text-muted-foreground bg-background px-2",children:t})}),(0,s.jsx)("div",{className:"flex-1 border-t border-border/40"})]})};var f=r(9385),b=r(1007),v=r(3917);let N=e=>{let{value:t,onChange:r,error:a,disabled:i=!1,className:n}=e,o=[{value:f.g.PATIENT,label:"Patient",description:"Book appointments and manage your dental care",icon:b.A},{value:f.g.DENTIST,label:"Dentist",description:"Manage practice, patients, and appointments",icon:v.A}];return(0,s.jsxs)("div",{className:(0,l.cn)("space-y-3",n),children:[(0,s.jsxs)("label",{className:"text-sm font-medium text-foreground",children:["I am a ",(0,s.jsx)("span",{className:"text-destructive",children:"*"})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3",children:o.map(e=>{let n=e.icon,o=t===e.value;return(0,s.jsx)("button",{type:"button",onClick:()=>!i&&r(e.value),disabled:i,className:(0,l.cn)("p-4 rounded-lg border-2 transition-all duration-200","text-left hover:bg-accent/50 focus:outline-none focus:ring-2 focus:ring-primary/50","disabled:opacity-50 disabled:cursor-not-allowed",o?"border-primary bg-primary/10 text-foreground":"border-border/40 bg-background/50 text-muted-foreground hover:text-foreground",a&&"border-destructive"),children:(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,s.jsx)("div",{className:(0,l.cn)("w-8 h-8 rounded-lg flex items-center justify-center",o?"bg-primary/20 text-primary":"bg-muted text-muted-foreground"),children:(0,s.jsx)(n,{className:"w-4 h-4"})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("h3",{className:(0,l.cn)("font-medium text-sm","text-foreground"),children:e.label}),(0,s.jsx)("p",{className:(0,l.cn)("text-xs mt-1","text-muted-foreground"),children:e.description})]})]})},e.value)})}),a&&(0,s.jsx)("p",{className:"text-sm text-destructive",role:"alert",children:a})]})};var j=r(6874),y=r.n(j),w=r(5196);let A=e=>{let{checked:t,onChange:r,error:a,disabled:i=!1,className:n}=e;return(0,s.jsxs)("div",{className:(0,l.cn)("space-y-2",n),children:[(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,s.jsx)("button",{type:"button",onClick:()=>!i&&r(!t),disabled:i,className:(0,l.cn)("w-5 h-5 rounded border-2 flex items-center justify-center transition-all duration-200","focus:outline-none focus:ring-2 focus:ring-primary/50","disabled:opacity-50 disabled:cursor-not-allowed",t?"bg-primary border-primary text-primary-foreground":"border-border/40 bg-background hover:border-primary/50",a&&"border-destructive"),"aria-checked":t,role:"checkbox",children:t&&(0,s.jsx)(w.A,{className:"w-3 h-3"})}),(0,s.jsx)("div",{className:"flex-1 text-sm",children:(0,s.jsxs)("label",{className:(0,l.cn)("text-muted-foreground leading-relaxed cursor-pointer",i&&"cursor-not-allowed"),children:["I agree to the"," ",(0,s.jsx)(y(),{href:"/legal/terms",className:"text-primary hover:text-primary/80 underline",target:"_blank",rel:"noopener noreferrer",children:"Terms of Service"})," ","and"," ",(0,s.jsx)(y(),{href:"/legal/privacy",className:"text-primary hover:text-primary/80 underline",target:"_blank",rel:"noopener noreferrer",children:"Privacy Policy"})," ",(0,s.jsx)("span",{className:"text-destructive",children:"*"})]})})]}),a&&(0,s.jsx)("p",{className:"text-sm text-destructive ml-8",role:"alert",children:a})]})}},9385:(e,t,r)=>{r.d(t,{g:()=>s});var s=function(e){return e.PATIENT="PATIENT",e.DENTIST="DENTIST",e.ADMIN="ADMIN",e}({})}}]);