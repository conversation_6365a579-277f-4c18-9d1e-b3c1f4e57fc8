(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[105],{5646:(e,s,t)=>{Promise.resolve().then(t.bind(t,8155))},6126:(e,s,t)=>{"use strict";t.d(s,{E:()=>d});var a=t(5155);t(2115);var r=t(9708),i=t(2085),n=t(2911);let l=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d(e){let{className:s,variant:t,asChild:i=!1,...d}=e,o=i?r.DX:"span";return(0,a.jsx)(o,{"data-slot":"badge",className:(0,n.cn)(l({variant:t}),s),...d})}},8155:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>w});var a=t(5155),r=t(2115),i=t(8366),n=t(5695),l=t(3294),d=t(9385);let o=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},s=(0,n.useRouter)(),{user:t,isAuthenticated:a,isLoading:i}=(0,l.As)(),{checkPermission:d,hasRole:o}=(0,l.BG)(),{requiredRole:m,requiredPermissions:u=[],redirectTo:x="/auth/login",onUnauthorized:p}=e;return(0,r.useEffect)(()=>{if(!i){if(!a||!t)return void(p?p():s.push(x));if(m&&!o(m)){if(p)p();else{let e=c(t.role);s.push(e)}return}if(u.length>0&&!u.every(e=>d(e)))return void(p?p():s.push("/unauthorized"))}},[a,t,i,m,u,x,p,s,d,o]),{isAuthenticated:a,user:t,isLoading:i,isAuthorized:a&&(!m||o(m))&&(0===u.length||u.every(e=>d(e)))}},c=e=>{switch(e){case d.g.PATIENT:return"/patient/dashboard";case d.g.DENTIST:return"/dentist/dashboard";case d.g.ADMIN:return"/admin/dashboard";default:return"/dashboard"}};var m=t(6695),u=t(285),x=t(6126),p=t(9074),g=t(7580),h=t(7434),f=t(4186),b=t(4616),v=t(3109),j=t(5339),N=t(646);let w=()=>{let{setPageTitle:e,setBreadcrumbs:s}=(0,i.Sw)(),{isAuthorized:t}=o();if((0,r.useEffect)(()=>{e("Dashboard"),s([{label:"Dashboard",current:!0}])},[e,s]),!t)return null;let n=[{title:"Today&apos;s Appointments",value:"8",description:"2 pending confirmations",icon:p.A,trend:"+12%",color:"text-blue-600",bgColor:"bg-blue-50"},{title:"Total Patients",value:"1,234",description:"23 new this month",icon:g.A,trend:"+5%",color:"text-green-600",bgColor:"bg-green-50"},{title:"Pending Documents",value:"12",description:"3 require urgent review",icon:h.A,trend:"-8%",color:"text-orange-600",bgColor:"bg-orange-50"},{title:"Average Wait Time",value:"15 min",description:"Down from last week",icon:f.A,trend:"-3 min",color:"text-purple-600",bgColor:"bg-purple-50"}],l=e=>{switch(e){case"confirmed":return(0,a.jsx)(x.E,{variant:"default",className:"bg-green-100 text-green-800",children:"Confirmed"});case"pending":return(0,a.jsx)(x.E,{variant:"secondary",className:"bg-yellow-100 text-yellow-800",children:"Pending"});default:return(0,a.jsx)(x.E,{variant:"outline",children:e})}};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-foreground",children:"Welcome back!"}),(0,a.jsx)("p",{className:"text-muted-foreground mt-1",children:"Here's what's happening at your practice today."})]}),(0,a.jsxs)(u.$,{className:"glass-button",children:[(0,a.jsx)(b.A,{className:"mr-2 h-4 w-4"}),"New Appointment"]})]}),(0,a.jsx)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-4",children:n.map((e,s)=>(0,a.jsxs)(m.Zp,{className:"glass-card",children:[(0,a.jsxs)(m.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(m.ZB,{className:"text-sm font-medium text-muted-foreground",children:e.title}),(0,a.jsx)("div",{className:"p-2 rounded-lg ".concat(e.bgColor),children:(0,a.jsx)(e.icon,{className:"h-4 w-4 ".concat(e.color)})})]}),(0,a.jsxs)(m.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-foreground",children:e.value}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-xs text-muted-foreground",children:[(0,a.jsx)("span",{children:e.description}),(0,a.jsx)(x.E,{variant:"outline",className:"text-xs",children:e.trend})]})]})]},s))}),(0,a.jsxs)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:[(0,a.jsxs)(m.Zp,{className:"glass-card lg:col-span-2",children:[(0,a.jsxs)(m.aR,{children:[(0,a.jsxs)(m.ZB,{className:"flex items-center space-x-2",children:[(0,a.jsx)(p.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"Today's Appointments"})]}),(0,a.jsx)(m.BT,{children:"Manage your appointments for today"})]}),(0,a.jsxs)(m.Wu,{children:[(0,a.jsx)("div",{className:"space-y-4",children:[{id:"1",patient:"John Doe",time:"09:00 AM",type:"Cleaning",status:"confirmed"},{id:"2",patient:"Jane Smith",time:"10:30 AM",type:"Consultation",status:"pending"},{id:"3",patient:"Mike Johnson",time:"02:00 PM",type:"Root Canal",status:"confirmed"},{id:"4",patient:"Sarah Wilson",time:"03:30 PM",type:"Filling",status:"confirmed"}].map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-lg border border-border/40 hover:bg-accent/50 transition-colors",children:[(0,a.jsx)("div",{className:"flex items-center space-x-3",children:(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:"font-medium text-foreground",children:e.patient}),(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:e.type})]})}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-foreground",children:e.time}),l(e.status)]})]},e.id))}),(0,a.jsx)("div",{className:"mt-4 pt-4 border-t border-border/40",children:(0,a.jsx)(u.$,{variant:"outline",className:"w-full",children:"View All Appointments"})})]})]}),(0,a.jsxs)(m.Zp,{className:"glass-card",children:[(0,a.jsxs)(m.aR,{children:[(0,a.jsx)(m.ZB,{children:"Quick Actions"}),(0,a.jsx)(m.BT,{children:"Common tasks and shortcuts"})]}),(0,a.jsxs)(m.Wu,{className:"space-y-3",children:[(0,a.jsxs)(u.$,{variant:"outline",className:"w-full justify-start",children:[(0,a.jsx)(b.A,{className:"mr-2 h-4 w-4"}),"Add New Patient"]}),(0,a.jsxs)(u.$,{variant:"outline",className:"w-full justify-start",children:[(0,a.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"Schedule Appointment"]}),(0,a.jsxs)(u.$,{variant:"outline",className:"w-full justify-start",children:[(0,a.jsx)(h.A,{className:"mr-2 h-4 w-4"}),"Upload Document"]}),(0,a.jsxs)(u.$,{variant:"outline",className:"w-full justify-start",children:[(0,a.jsx)(v.A,{className:"mr-2 h-4 w-4"}),"View Reports"]})]})]})]}),(0,a.jsxs)("div",{className:"grid gap-6 md:grid-cols-2",children:[(0,a.jsxs)(m.Zp,{className:"glass-card",children:[(0,a.jsx)(m.aR,{children:(0,a.jsxs)(m.ZB,{className:"flex items-center space-x-2 text-orange-600",children:[(0,a.jsx)(j.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"Pending Actions"})]})}),(0,a.jsxs)(m.Wu,{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 p-2 rounded-lg bg-orange-50 border border-orange-200",children:[(0,a.jsx)(j.A,{className:"h-4 w-4 text-orange-600"}),(0,a.jsx)("span",{className:"text-sm text-orange-800",children:"3 appointment confirmations needed"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3 p-2 rounded-lg bg-red-50 border border-red-200",children:[(0,a.jsx)(j.A,{className:"h-4 w-4 text-red-600"}),(0,a.jsx)("span",{className:"text-sm text-red-800",children:"2 overdue patient follow-ups"})]})]})]}),(0,a.jsxs)(m.Zp,{className:"glass-card",children:[(0,a.jsx)(m.aR,{children:(0,a.jsxs)(m.ZB,{className:"flex items-center space-x-2 text-green-600",children:[(0,a.jsx)(N.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"Recent Completions"})]})}),(0,a.jsxs)(m.Wu,{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 p-2 rounded-lg bg-green-50 border border-green-200",children:[(0,a.jsx)(N.A,{className:"h-4 w-4 text-green-600"}),(0,a.jsx)("span",{className:"text-sm text-green-800",children:"5 appointments completed today"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3 p-2 rounded-lg bg-blue-50 border border-blue-200",children:[(0,a.jsx)(N.A,{className:"h-4 w-4 text-blue-600"}),(0,a.jsx)("span",{className:"text-sm text-blue-800",children:"12 documents processed this week"})]})]})]})]})]})}},8366:(e,s,t)=>{"use strict";t.d(s,{E$:()=>o,Sw:()=>c,cL:()=>d}),t(2115);var a=t(5453),r=t(6786);t(9385);var i=function(e){return e.SUCCESS="success",e.ERROR="error",e.WARNING="warning",e.INFO="info",e}({});let n=()=>"notification-".concat(Date.now(),"-").concat(Math.random().toString(36).substr(2,9)),l=(0,a.v)()((0,r.Zr)((e,s)=>({theme:"dark",sidebarCollapsed:!1,sidebarOpen:!1,globalLoading:!1,loadingMessage:"",notifications:[],modals:{},pageTitle:"Dentist Appointment Platform",breadcrumbs:[],setTheme:s=>{e({theme:s});{let e=window.document.documentElement;if(e.classList.remove("light","dark"),"system"===s){let s=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";e.classList.add(s)}else e.classList.add(s)}},toggleSidebar:()=>{e(e=>({sidebarCollapsed:!e.sidebarCollapsed}))},setSidebarOpen:s=>{e({sidebarOpen:s})},setSidebarCollapsed:s=>{e({sidebarCollapsed:s})},setGlobalLoading:function(s){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";e({globalLoading:s,loadingMessage:t})},addNotification:t=>{let a={...t,id:n(),createdAt:new Date};e(e=>({notifications:[...e.notifications,a]})),a.duration&&a.duration>0&&setTimeout(()=>{s().removeNotification(a.id)},a.duration)},removeNotification:s=>{e(e=>({notifications:e.notifications.filter(e=>e.id!==s)}))},clearNotifications:()=>{e({notifications:[]})},openModal:s=>{e(e=>({modals:{...e.modals,[s]:!0}}))},closeModal:s=>{e(e=>({modals:{...e.modals,[s]:!1}}))},toggleModal:s=>{e(e=>({modals:{...e.modals,[s]:!e.modals[s]}}))},isModalOpen:e=>s().modals[e]||!1,setPageTitle:s=>{e({pageTitle:s}),document.title="".concat(s," | Dentist Appointment Platform")},setBreadcrumbs:s=>{e({breadcrumbs:s})},addBreadcrumb:s=>{e(e=>({breadcrumbs:[...e.breadcrumbs,s]}))},reset:()=>{e({sidebarCollapsed:!1,sidebarOpen:!1,globalLoading:!1,loadingMessage:"",notifications:[],modals:{},pageTitle:"Dentist Appointment Platform",breadcrumbs:[]})}}),{name:"ui-storage",storage:(0,r.KU)(()=>localStorage),partialize:e=>({theme:e.theme,sidebarCollapsed:e.sidebarCollapsed})})),d=()=>{let e=l(e=>e.sidebarCollapsed),s=l(e=>e.sidebarOpen),t=l(e=>e.toggleSidebar);return{sidebarCollapsed:e,sidebarOpen:s,toggleSidebar:t,setSidebarOpen:l(e=>e.setSidebarOpen),setSidebarCollapsed:l(e=>e.setSidebarCollapsed)}},o=()=>{let e=l(e=>e.notifications),s=l(e=>e.addNotification),t=l(e=>e.removeNotification);return{notifications:e,addNotification:s,removeNotification:t,clearNotifications:l(e=>e.clearNotifications),showSuccess:function(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5e3;s({type:i.SUCCESS,title:e,message:t,duration:a})},showError:function(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;s({type:i.ERROR,title:e,message:t,duration:a})},showWarning:function(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:7e3;s({type:i.WARNING,title:e,message:t,duration:a})},showInfo:function(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5e3;s({type:i.INFO,title:e,message:t,duration:a})}}},c=()=>{let e=l(e=>e.pageTitle),s=l(e=>e.breadcrumbs),t=l(e=>e.setPageTitle);return{pageTitle:e,breadcrumbs:s,setPageTitle:t,setBreadcrumbs:l(e=>e.setBreadcrumbs),addBreadcrumb:l(e=>e.addBreadcrumb)}}}},e=>{var s=s=>e(e.s=s);e.O(0,[52,624,60,441,684,358],()=>s(5646)),_N_E=e.O()}]);