(()=>{var e={};e.id=890,e.ids=[890],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},9103:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>h,tree:()=>o});var r=t(65239),a=t(48088),n=t(88170),i=t.n(n),l=t(30893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);t.d(s,c);let o={children:["",{children:["auth",{children:["test",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,11400)),"C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\app\\auth\\test\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\app\\auth\\test\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/auth/test/page",pathname:"/auth/test",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11400:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\test\\\\dentist-appointment-platform\\\\src\\\\app\\\\auth\\\\test\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\app\\auth\\test\\page.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},48652:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>g});var r=t(60687);t(43210);var a=t(82136),n=t(99720),i=t(29523),l=t(44493),c=t(96834),o=t(58869),d=t(99891),u=t(62688);let h=(0,u.A)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]]);var x=t(5336);let p=(0,u.A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);var m=t(93613);let g=()=>{let{data:e,status:s}=(0,a.useSession)(),{user:t,isAuthenticated:u,isLoading:g}=(0,n.As)(),j=[{name:"NextAuth Session",status:"authenticated"===s?"pass":"loading"===s?"loading":"fail",description:`Status: ${s}`,icon:o.A},{name:"Zustand Store Sync",status:u&&t?"pass":g?"loading":"fail",description:`Authenticated: ${u}, User: ${t?"Present":"None"}`,icon:d.A},{name:"Route Protection",status:"pass",description:"Auth test page accessible",icon:h}],f=e=>{switch(e){case"pass":return(0,r.jsx)(x.A,{className:"w-5 h-5 text-green-500"});case"fail":return(0,r.jsx)(p,{className:"w-5 h-5 text-red-500"});case"loading":return(0,r.jsx)(m.A,{className:"w-5 h-5 text-yellow-500"});default:return(0,r.jsx)(m.A,{className:"w-5 h-5 text-gray-500"})}},v=e=>{switch(e){case"pass":return(0,r.jsx)(c.E,{className:"bg-green-100 text-green-800",children:"Pass"});case"fail":return(0,r.jsx)(c.E,{variant:"destructive",children:"Fail"});case"loading":return(0,r.jsx)(c.E,{variant:"secondary",children:"Loading"});default:return(0,r.jsx)(c.E,{variant:"outline",children:"Unknown"})}};return(0,r.jsxs)("div",{className:"container mx-auto p-6 space-y-6",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-foreground mb-2",children:"Authentication System Test"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Verify all authentication components are working correctly"})]}),(0,r.jsx)("div",{className:"grid gap-4 md:grid-cols-3",children:j.map((e,s)=>{let t=e.icon;return(0,r.jsxs)(l.Zp,{className:"glass-card",children:[(0,r.jsx)(l.aR,{className:"pb-3",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(t,{className:"w-5 h-5"}),(0,r.jsx)(l.ZB,{className:"text-sm",children:e.name})]}),f(e.status)]})}),(0,r.jsxs)(l.Wu,{children:[(0,r.jsx)(l.BT,{className:"text-xs mb-2",children:e.description}),v(e.status)]})]},s)})}),(0,r.jsxs)(l.Zp,{className:"glass-card",children:[(0,r.jsxs)(l.aR,{children:[(0,r.jsx)(l.ZB,{children:"Session Information"}),(0,r.jsx)(l.BT,{children:"Current authentication state"})]}),(0,r.jsx)(l.Wu,{className:"space-y-4",children:(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold mb-2",children:"NextAuth Session"}),(0,r.jsx)("pre",{className:"text-xs bg-muted p-3 rounded overflow-auto",children:JSON.stringify(e,null,2)})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold mb-2",children:"Zustand Store"}),(0,r.jsx)("pre",{className:"text-xs bg-muted p-3 rounded overflow-auto",children:JSON.stringify({user:t,isAuthenticated:u,isLoading:g},null,2)})]})]})})]}),(0,r.jsxs)(l.Zp,{className:"glass-card",children:[(0,r.jsxs)(l.aR,{children:[(0,r.jsx)(l.ZB,{children:"Authentication Actions"}),(0,r.jsx)(l.BT,{children:"Test authentication functionality"})]}),(0,r.jsxs)(l.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex flex-wrap gap-3",children:[!e&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(i.$,{onClick:()=>(0,a.signIn)("credentials"),variant:"outline",children:"Test Credentials Login"}),(0,r.jsx)(i.$,{onClick:()=>(0,a.signIn)("google"),className:"glass-button",children:"Test Google OAuth"})]}),e&&(0,r.jsx)(i.$,{onClick:()=>(0,a.signOut)(),variant:"destructive",children:"Sign Out"})]}),t&&(0,r.jsxs)("div",{className:"mt-4 p-4 bg-muted/50 rounded-lg",children:[(0,r.jsx)("h4",{className:"font-semibold mb-2",children:"User Details"}),(0,r.jsxs)("div",{className:"grid gap-2 text-sm",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"ID:"})," ",t.id]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"Email:"})," ",t.email]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"Name:"})," ",t.name]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"Role:"})," ",t.role]}),t.image&&(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"Image:"})," ",t.image]})]})]})]})]}),(0,r.jsxs)(l.Zp,{className:"glass-card",children:[(0,r.jsxs)(l.aR,{children:[(0,r.jsx)(l.ZB,{children:"Navigation Test"}),(0,r.jsx)(l.BT,{children:"Test protected routes and redirects"})]}),(0,r.jsx)(l.Wu,{children:(0,r.jsxs)("div",{className:"flex flex-wrap gap-3",children:[(0,r.jsx)(i.$,{onClick:()=>window.location.href="/dashboard",variant:"outline",children:"Test Dashboard (Protected)"}),(0,r.jsx)(i.$,{onClick:()=>window.location.href="/auth/login",variant:"outline",children:"Test Login Page"}),(0,r.jsx)(i.$,{onClick:()=>window.location.href="/auth/register",variant:"outline",children:"Test Register Page"}),(0,r.jsx)(i.$,{onClick:()=>window.location.href="/unauthorized",variant:"outline",children:"Test Unauthorized Page"})]})})]})]})}},49480:(e,s,t)=>{Promise.resolve().then(t.bind(t,48652))},62632:(e,s,t)=>{Promise.resolve().then(t.bind(t,11400))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},79551:e=>{"use strict";e.exports=require("url")},93613:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[243,97,658,513],()=>t(9103));module.exports=r})();