(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[305],{854:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var s=r(5155);r(2115);var a=r(5695);let n=(0,r(9946).A)("shield-x",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m14.5 9.5-5 5",key:"17q4r4"}],["path",{d:"m9.5 9.5 5 5",key:"18nt4w"}]]);var l=r(7550),i=r(7340),o=r(285),c=r(6695),u=r(3294);let d=()=>{let e=(0,a.useRouter)(),{user:t}=(0,u.As)();return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-background via-background to-secondary/20 flex items-center justify-center p-6",children:(0,s.jsxs)(c.Zp,{className:"glass-card w-full max-w-md",children:[(0,s.jsxs)(c.aR,{className:"text-center",children:[(0,s.jsx)("div",{className:"mx-auto w-16 h-16 bg-destructive/20 rounded-full flex items-center justify-center mb-4",children:(0,s.jsx)(n,{className:"w-8 h-8 text-destructive"})}),(0,s.jsx)(c.ZB,{className:"text-2xl font-bold text-foreground",children:"Access Denied"}),(0,s.jsx)(c.BT,{className:"text-muted-foreground",children:"You don't have permission to access this page."})]}),(0,s.jsxs)(c.Wu,{className:"space-y-4",children:[(0,s.jsx)("div",{className:"text-center text-sm text-muted-foreground",children:(0,s.jsx)("p",{children:"This page requires specific permissions that your account doesn't have. Please contact your administrator if you believe this is an error."})}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3",children:[(0,s.jsxs)(o.$,{variant:"outline",onClick:()=>{e.back()},className:"flex-1",children:[(0,s.jsx)(l.A,{className:"w-4 h-4 mr-2"}),"Go Back"]}),(0,s.jsxs)(o.$,{onClick:()=>{if(t)switch(t.role){case"PATIENT":e.push("/patient/dashboard");break;case"DENTIST":e.push("/dentist/dashboard");break;case"ADMIN":e.push("/admin/dashboard");break;default:e.push("/dashboard")}else e.push("/")},className:"flex-1 glass-button",children:[(0,s.jsx)(i.A,{className:"w-4 h-4 mr-2"}),"Home"]})]}),t&&(0,s.jsxs)("div",{className:"mt-6 p-3 bg-muted/50 rounded-lg text-center",children:[(0,s.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Logged in as: ",(0,s.jsx)("span",{className:"font-medium",children:t.email})]}),(0,s.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Role: ",(0,s.jsx)("span",{className:"font-medium capitalize",children:t.role.toLowerCase()})]})]})]})]})})}},4686:(e,t,r)=>{Promise.resolve().then(r.bind(r,854))},5453:(e,t,r)=>{"use strict";r.d(t,{v:()=>o});var s=r(2115);let a=e=>{let t,r=new Set,s=(e,s)=>{let a="function"==typeof e?e(t):e;if(!Object.is(a,t)){let e=t;t=(null!=s?s:"object"!=typeof a||null===a)?a:Object.assign({},t,a),r.forEach(r=>r(t,e))}},a=()=>t,n={setState:s,getState:a,getInitialState:()=>l,subscribe:e=>(r.add(e),()=>r.delete(e))},l=t=e(s,a,n);return n},n=e=>e?a(e):a,l=e=>e,i=e=>{let t=n(e),r=e=>(function(e,t=l){let r=s.useSyncExternalStore(e.subscribe,()=>t(e.getState()),()=>t(e.getInitialState()));return s.useDebugValue(r),r})(t,e);return Object.assign(r,t),r},o=e=>e?i(e):i},5695:(e,t,r)=>{"use strict";var s=r(8999);r.o(s,"usePathname")&&r.d(t,{usePathname:function(){return s.usePathname}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}})},6786:(e,t,r)=>{"use strict";function s(e,t){let r;try{r=e()}catch(e){return}return{getItem:e=>{var s;let a=e=>null===e?null:JSON.parse(e,null==t?void 0:t.reviver),n=null!=(s=r.getItem(e))?s:null;return n instanceof Promise?n.then(a):a(n)},setItem:(e,s)=>r.setItem(e,JSON.stringify(s,null==t?void 0:t.replacer)),removeItem:e=>r.removeItem(e)}}r.d(t,{KU:()=>s,Zr:()=>n});let a=e=>t=>{try{let r=e(t);if(r instanceof Promise)return r;return{then:e=>a(e)(r),catch(e){return this}}}catch(e){return{then(e){return this},catch:t=>a(t)(e)}}},n=(e,t)=>(r,n,l)=>{let i,o={storage:s(()=>localStorage),partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},c=!1,u=new Set,d=new Set,m=o.storage;if(!m)return e((...e)=>{console.warn(`[zustand persist middleware] Unable to update item '${o.name}', the given storage is currently unavailable.`),r(...e)},n,l);let h=()=>{let e=o.partialize({...n()});return m.setItem(o.name,{state:e,version:o.version})},f=l.setState;l.setState=(e,t)=>{f(e,t),h()};let v=e((...e)=>{r(...e),h()},n,l);l.getInitialState=()=>v;let x=()=>{var e,t;if(!m)return;c=!1,u.forEach(e=>{var t;return e(null!=(t=n())?t:v)});let s=(null==(t=o.onRehydrateStorage)?void 0:t.call(o,null!=(e=n())?e:v))||void 0;return a(m.getItem.bind(m))(o.name).then(e=>{if(e)if("number"!=typeof e.version||e.version===o.version)return[!1,e.state];else{if(o.migrate){let t=o.migrate(e.state,e.version);return t instanceof Promise?t.then(e=>[!0,e]):[!0,t]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}return[!1,void 0]}).then(e=>{var t;let[s,a]=e;if(r(i=o.merge(a,null!=(t=n())?t:v),!0),s)return h()}).then(()=>{null==s||s(i,void 0),i=n(),c=!0,d.forEach(e=>e(i))}).catch(e=>{null==s||s(void 0,e)})};return l.persist={setOptions:e=>{o={...o,...e},e.storage&&(m=e.storage)},clearStorage:()=>{null==m||m.removeItem(o.name)},getOptions:()=>o,rehydrate:()=>x(),hasHydrated:()=>c,onHydrate:e=>(u.add(e),()=>{u.delete(e)}),onFinishHydration:e=>(d.add(e),()=>{d.delete(e)})},o.skipHydration||x(),i||v}},7340:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},7550:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[52,60,441,684,358],()=>t(4686)),_N_E=e.O()}]);