{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/test/dentist-appointment-platform/src/components/layout/AuthLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { motion } from 'framer-motion';\nimport { ArrowLeft, Shield, Heart, Users } from 'lucide-react';\n\ninterface AuthLayoutProps {\n  children: React.ReactNode;\n  title: string;\n  subtitle: string;\n  showBackButton?: boolean;\n  backHref?: string;\n}\n\nconst AuthLayout: React.FC<AuthLayoutProps> = ({\n  children,\n  title,\n  subtitle,\n  showBackButton = true,\n  backHref = '/',\n}) => {\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-background via-background to-secondary/20 flex flex-col lg:flex-row\">\n      {/* Left Side - Branding & Features */}\n      <div className=\"hidden lg:flex lg:w-1/2 xl:w-2/5 flex-col justify-center p-12 relative overflow-hidden\">\n        {/* Background decoration */}\n        <div className=\"absolute inset-0 bg-gradient-to-br from-primary/10 via-transparent to-accent/10\" />\n        <div className=\"absolute top-1/4 left-1/4 w-64 h-64 bg-primary/20 rounded-full blur-3xl\" />\n        <div className=\"absolute bottom-1/4 right-1/4 w-48 h-48 bg-accent/20 rounded-full blur-3xl\" />\n        \n        <div className=\"relative z-10\">\n          {/* Logo/Brand */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            className=\"mb-12\"\n          >\n            <div className=\"flex items-center space-x-3 mb-6\">\n              <div className=\"w-12 h-12 bg-primary/20 rounded-xl flex items-center justify-center backdrop-blur-sm border border-primary/30\">\n                <Heart className=\"w-6 h-6 text-primary\" />\n              </div>\n              <div>\n                <h1 className=\"text-2xl font-bold text-foreground font-poppins\">\n                  DentCare Pro\n                </h1>\n                <p className=\"text-sm text-muted-foreground\">\n                  Professional Dental Management\n                </p>\n              </div>\n            </div>\n          </motion.div>\n\n          {/* Features */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.2 }}\n            className=\"space-y-8\"\n          >\n            <div>\n              <h2 className=\"text-3xl font-bold text-foreground mb-4 font-poppins\">\n                Secure & Compliant\n              </h2>\n              <p className=\"text-lg text-muted-foreground leading-relaxed\">\n                HIPAA-compliant platform designed specifically for dental practices.\n                Manage appointments, patient records, and documents with confidence.\n              </p>\n            </div>\n\n            <div className=\"space-y-6\">\n              <div className=\"flex items-start space-x-4\">\n                <div className=\"w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center\">\n                  <Shield className=\"w-5 h-5 text-green-400\" />\n                </div>\n                <div>\n                  <h3 className=\"font-semibold text-foreground mb-1\">\n                    Enterprise Security\n                  </h3>\n                  <p className=\"text-sm text-muted-foreground\">\n                    End-to-end encryption and secure data handling\n                  </p>\n                </div>\n              </div>\n\n              <div className=\"flex items-start space-x-4\">\n                <div className=\"w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center\">\n                  <Users className=\"w-5 h-5 text-blue-400\" />\n                </div>\n                <div>\n                  <h3 className=\"font-semibold text-foreground mb-1\">\n                    Multi-Role Access\n                  </h3>\n                  <p className=\"text-sm text-muted-foreground\">\n                    Tailored interfaces for patients, dentists, and staff\n                  </p>\n                </div>\n              </div>\n\n              <div className=\"flex items-start space-x-4\">\n                <div className=\"w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center\">\n                  <Heart className=\"w-5 h-5 text-purple-400\" />\n                </div>\n                <div>\n                  <h3 className=\"font-semibold text-foreground mb-1\">\n                    Patient-Centered\n                  </h3>\n                  <p className=\"text-sm text-muted-foreground\">\n                    Streamlined experience for better patient care\n                  </p>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Right Side - Authentication Form */}\n      <div className=\"flex-1 flex flex-col justify-center p-6 sm:p-12 lg:p-16\">\n        <div className=\"w-full max-w-md mx-auto\">\n          {/* Back Button */}\n          {showBackButton && (\n            <motion.div\n              initial={{ opacity: 0, x: -20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.4 }}\n              className=\"mb-8\"\n            >\n              <Link\n                href={backHref}\n                className=\"inline-flex items-center space-x-2 text-muted-foreground hover:text-foreground transition-colors\"\n              >\n                <ArrowLeft className=\"w-4 h-4\" />\n                <span className=\"text-sm\">Back to home</span>\n              </Link>\n            </motion.div>\n          )}\n\n          {/* Form Container */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.1 }}\n            className=\"glass-card p-8 sm:p-10\"\n          >\n            {/* Header */}\n            <div className=\"text-center mb-8\">\n              <h1 className=\"text-3xl font-bold text-foreground mb-2 font-poppins\">\n                {title}\n              </h1>\n              <p className=\"text-muted-foreground\">\n                {subtitle}\n              </p>\n            </div>\n\n            {/* Form Content */}\n            {children}\n          </motion.div>\n\n          {/* Mobile Branding */}\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ duration: 0.6, delay: 0.3 }}\n            className=\"lg:hidden mt-8 text-center\"\n          >\n            <p className=\"text-xs text-muted-foreground\">\n              © 2024 DentCare Pro. HIPAA-compliant dental practice management.\n            </p>\n          </motion.div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AuthLayout;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AALA;;;;;AAeA,MAAM,aAAwC,CAAC,EAC7C,QAAQ,EACR,KAAK,EACL,QAAQ,EACR,iBAAiB,IAAI,EACrB,WAAW,GAAG,EACf;IACC,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCAEf,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAkD;;;;;;8DAGhE,6LAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;;;;;;0CAQnD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,WAAU;;kDAEV,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAuD;;;;;;0DAGrE,6LAAC;gDAAE,WAAU;0DAAgD;;;;;;;;;;;;kDAM/D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;kEAEpB,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAqC;;;;;;0EAGnD,6LAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;;;;;;;0DAMjD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;;;;;;kEAEnB,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAqC;;;;;;0EAGnD,6LAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;;;;;;;0DAMjD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;;;;;;kEAEnB,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAqC;;;;;;0EAGnD,6LAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWzD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;wBAEZ,gCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;sCAEV,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAM;gCACN,WAAU;;kDAEV,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;;;;;;sCAMhC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAGV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDACX;;;;;;sDAEH,6LAAC;4CAAE,WAAU;sDACV;;;;;;;;;;;;gCAKJ;;;;;;;sCAIH,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;sCAEV,cAAA,6LAAC;gCAAE,WAAU;0CAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzD;KAhKM;uCAkKS", "debugId": null}}, {"offset": {"line": 476, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/test/dentist-appointment-platform/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 508, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/test/dentist-appointment-platform/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 542, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/test/dentist-appointment-platform/src/components/forms/AuthFormField.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { Eye, EyeOff } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\n\ninterface AuthFormFieldProps {\n  label: string;\n  type?: 'text' | 'email' | 'password' | 'tel';\n  placeholder?: string;\n  value: string;\n  onChange: (value: string) => void;\n  error?: string;\n  required?: boolean;\n  disabled?: boolean;\n  className?: string;\n  autoComplete?: string;\n  id?: string;\n}\n\nconst AuthFormField: React.FC<AuthFormFieldProps> = ({\n  label,\n  type = 'text',\n  placeholder,\n  value,\n  onChange,\n  error,\n  required = false,\n  disabled = false,\n  className,\n  autoComplete,\n  id,\n}) => {\n  const [showPassword, setShowPassword] = useState(false);\n  const fieldId = id || `field-${label.toLowerCase().replace(/\\s+/g, '-')}`;\n  \n  const isPassword = type === 'password';\n  const inputType = isPassword && showPassword ? 'text' : type;\n\n  return (\n    <div className={cn('space-y-2', className)}>\n      <Label \n        htmlFor={fieldId}\n        className={cn(\n          'text-sm font-medium text-foreground',\n          error && 'text-destructive'\n        )}\n      >\n        {label}\n        {required && <span className=\"text-destructive ml-1\">*</span>}\n      </Label>\n      \n      <div className=\"relative\">\n        <Input\n          id={fieldId}\n          type={inputType}\n          placeholder={placeholder}\n          value={value}\n          onChange={(e) => onChange(e.target.value)}\n          disabled={disabled}\n          autoComplete={autoComplete}\n          className={cn(\n            'glass-input w-full',\n            error && 'border-destructive focus:border-destructive',\n            isPassword && 'pr-10'\n          )}\n          aria-invalid={!!error}\n          aria-describedby={error ? `${fieldId}-error` : undefined}\n        />\n        \n        {isPassword && (\n          <button\n            type=\"button\"\n            onClick={() => setShowPassword(!showPassword)}\n            className=\"absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors\"\n            tabIndex={-1}\n            aria-label={showPassword ? 'Hide password' : 'Show password'}\n          >\n            {showPassword ? (\n              <EyeOff className=\"w-4 h-4\" />\n            ) : (\n              <Eye className=\"w-4 h-4\" />\n            )}\n          </button>\n        )}\n      </div>\n      \n      {error && (\n        <p \n          id={`${fieldId}-error`}\n          className=\"text-sm text-destructive\"\n          role=\"alert\"\n        >\n          {error}\n        </p>\n      )}\n    </div>\n  );\n};\n\nexport default AuthFormField;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;;;AANA;;;;;;AAsBA,MAAM,gBAA8C,CAAC,EACnD,KAAK,EACL,OAAO,MAAM,EACb,WAAW,EACX,KAAK,EACL,QAAQ,EACR,KAAK,EACL,WAAW,KAAK,EAChB,WAAW,KAAK,EAChB,SAAS,EACT,YAAY,EACZ,EAAE,EACH;;IACC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,UAAU,MAAM,CAAC,MAAM,EAAE,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,MAAM;IAEzE,MAAM,aAAa,SAAS;IAC5B,MAAM,YAAY,cAAc,eAAe,SAAS;IAExD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;0BAC9B,6LAAC,oIAAA,CAAA,QAAK;gBACJ,SAAS;gBACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uCACA,SAAS;;oBAGV;oBACA,0BAAY,6LAAC;wBAAK,WAAU;kCAAwB;;;;;;;;;;;;0BAGvD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oIAAA,CAAA,QAAK;wBACJ,IAAI;wBACJ,MAAM;wBACN,aAAa;wBACb,OAAO;wBACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wBACxC,UAAU;wBACV,cAAc;wBACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sBACA,SAAS,+CACT,cAAc;wBAEhB,gBAAc,CAAC,CAAC;wBAChB,oBAAkB,QAAQ,GAAG,QAAQ,MAAM,CAAC,GAAG;;;;;;oBAGhD,4BACC,6LAAC;wBACC,MAAK;wBACL,SAAS,IAAM,gBAAgB,CAAC;wBAChC,WAAU;wBACV,UAAU,CAAC;wBACX,cAAY,eAAe,kBAAkB;kCAE5C,6BACC,6LAAC,6MAAA,CAAA,SAAM;4BAAC,WAAU;;;;;iDAElB,6LAAC,mMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;;;;;;;;;;;;YAMtB,uBACC,6LAAC;gBACC,IAAI,GAAG,QAAQ,MAAM,CAAC;gBACtB,WAAU;gBACV,MAAK;0BAEJ;;;;;;;;;;;;AAKX;GA9EM;KAAA;uCAgFS", "debugId": null}}, {"offset": {"line": 668, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/test/dentist-appointment-platform/src/components/forms/AuthSubmitButton.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Loader2 } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { Button } from '@/components/ui/button';\n\ninterface AuthSubmitButtonProps {\n  children: React.ReactNode;\n  isLoading?: boolean;\n  disabled?: boolean;\n  type?: 'submit' | 'button';\n  variant?: 'default' | 'outline' | 'ghost';\n  size?: 'sm' | 'default' | 'lg';\n  className?: string;\n  onClick?: () => void;\n  loadingText?: string;\n}\n\nconst AuthSubmitButton: React.FC<AuthSubmitButtonProps> = ({\n  children,\n  isLoading = false,\n  disabled = false,\n  type = 'submit',\n  variant = 'default',\n  size = 'default',\n  className,\n  onClick,\n  loadingText = 'Please wait...',\n}) => {\n  const isDisabled = disabled || isLoading;\n\n  return (\n    <Button\n      type={type}\n      variant={variant}\n      size={size}\n      disabled={isDisabled}\n      onClick={onClick}\n      className={cn(\n        'glass-button w-full relative',\n        'transition-all duration-300 ease-in-out',\n        'disabled:opacity-50 disabled:cursor-not-allowed',\n        className\n      )}\n    >\n      {isLoading && (\n        <Loader2 className=\"w-4 h-4 mr-2 animate-spin\" />\n      )}\n      {isLoading ? loadingText : children}\n    </Button>\n  );\n};\n\nexport default AuthSubmitButton;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AALA;;;;;AAmBA,MAAM,mBAAoD,CAAC,EACzD,QAAQ,EACR,YAAY,KAAK,EACjB,WAAW,KAAK,EAChB,OAAO,QAAQ,EACf,UAAU,SAAS,EACnB,OAAO,SAAS,EAChB,SAAS,EACT,OAAO,EACP,cAAc,gBAAgB,EAC/B;IACC,MAAM,aAAa,YAAY;IAE/B,qBACE,6LAAC,qIAAA,CAAA,SAAM;QACL,MAAM;QACN,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gCACA,2CACA,mDACA;;YAGD,2BACC,6LAAC,oNAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YAEpB,YAAY,cAAc;;;;;;;AAGjC;KAjCM;uCAmCS", "debugId": null}}, {"offset": {"line": 718, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/test/dentist-appointment-platform/src/components/forms/OAuthButton.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Loader2 } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { Button } from '@/components/ui/button';\n\ninterface OAuthButtonProps {\n  provider: 'google' | 'microsoft' | 'apple';\n  isLoading?: boolean;\n  disabled?: boolean;\n  onClick: () => void;\n  className?: string;\n  children?: React.ReactNode;\n}\n\nconst OAuthButton: React.FC<OAuthButtonProps> = ({\n  provider,\n  isLoading = false,\n  disabled = false,\n  onClick,\n  className,\n  children,\n}) => {\n  const isDisabled = disabled || isLoading;\n\n  const getProviderIcon = () => {\n    switch (provider) {\n      case 'google':\n        return (\n          <svg className=\"w-5 h-5\" viewBox=\"0 0 24 24\">\n            <path\n              fill=\"#4285F4\"\n              d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n            />\n            <path\n              fill=\"#34A853\"\n              d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n            />\n            <path\n              fill=\"#FBBC05\"\n              d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n            />\n            <path\n              fill=\"#EA4335\"\n              d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n            />\n          </svg>\n        );\n      case 'microsoft':\n        return (\n          <svg className=\"w-5 h-5\" viewBox=\"0 0 24 24\">\n            <path fill=\"#F25022\" d=\"M1 1h10v10H1z\" />\n            <path fill=\"#00A4EF\" d=\"M13 1h10v10H13z\" />\n            <path fill=\"#7FBA00\" d=\"M1 13h10v10H1z\" />\n            <path fill=\"#FFB900\" d=\"M13 13h10v10H13z\" />\n          </svg>\n        );\n      case 'apple':\n        return (\n          <svg className=\"w-5 h-5\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n            <path d=\"M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z\" />\n          </svg>\n        );\n      default:\n        return null;\n    }\n  };\n\n  const getProviderName = () => {\n    switch (provider) {\n      case 'google':\n        return 'Google';\n      case 'microsoft':\n        return 'Microsoft';\n      case 'apple':\n        return 'Apple';\n      default:\n        return provider;\n    }\n  };\n\n  return (\n    <Button\n      type=\"button\"\n      variant=\"outline\"\n      disabled={isDisabled}\n      onClick={onClick}\n      className={cn(\n        'w-full glass-input border-border/40 hover:bg-accent/50',\n        'transition-all duration-300 ease-in-out',\n        'disabled:opacity-50 disabled:cursor-not-allowed',\n        className\n      )}\n    >\n      {isLoading ? (\n        <Loader2 className=\"w-5 h-5 mr-3 animate-spin\" />\n      ) : (\n        <span className=\"mr-3\">{getProviderIcon()}</span>\n      )}\n      {children || `Continue with ${getProviderName()}`}\n    </Button>\n  );\n};\n\nexport default OAuthButton;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AALA;;;;;AAgBA,MAAM,cAA0C,CAAC,EAC/C,QAAQ,EACR,YAAY,KAAK,EACjB,WAAW,KAAK,EAChB,OAAO,EACP,SAAS,EACT,QAAQ,EACT;IACC,MAAM,aAAa,YAAY;IAE/B,MAAM,kBAAkB;QACtB,OAAQ;YACN,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;oBAAU,SAAQ;;sCAC/B,6LAAC;4BACC,MAAK;4BACL,GAAE;;;;;;sCAEJ,6LAAC;4BACC,MAAK;4BACL,GAAE;;;;;;sCAEJ,6LAAC;4BACC,MAAK;4BACL,GAAE;;;;;;sCAEJ,6LAAC;4BACC,MAAK;4BACL,GAAE;;;;;;;;;;;;YAIV,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;oBAAU,SAAQ;;sCAC/B,6LAAC;4BAAK,MAAK;4BAAU,GAAE;;;;;;sCACvB,6LAAC;4BAAK,MAAK;4BAAU,GAAE;;;;;;sCACvB,6LAAC;4BAAK,MAAK;4BAAU,GAAE;;;;;;sCACvB,6LAAC;4BAAK,MAAK;4BAAU,GAAE;;;;;;;;;;;;YAG7B,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;oBAAU,SAAQ;oBAAY,MAAK;8BAChD,cAAA,6LAAC;wBAAK,GAAE;;;;;;;;;;;YAGd;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kBAAkB;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QACL,MAAK;QACL,SAAQ;QACR,UAAU;QACV,SAAS;QACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA,2CACA,mDACA;;YAGD,0BACC,6LAAC,oNAAA,CAAA,UAAO;gBAAC,WAAU;;;;;qCAEnB,6LAAC;gBAAK,WAAU;0BAAQ;;;;;;YAEzB,YAAY,CAAC,cAAc,EAAE,mBAAmB;;;;;;;AAGvD;KAvFM;uCAyFS", "debugId": null}}, {"offset": {"line": 895, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/test/dentist-appointment-platform/src/components/forms/FormDivider.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface FormDividerProps {\n  text?: string;\n  className?: string;\n}\n\nconst FormDivider: React.FC<FormDividerProps> = ({\n  text = 'or',\n  className,\n}) => {\n  return (\n    <div className={cn('relative flex items-center', className)}>\n      <div className=\"flex-1 border-t border-border/40\" />\n      <div className=\"px-4\">\n        <span className=\"text-sm text-muted-foreground bg-background px-2\">\n          {text}\n        </span>\n      </div>\n      <div className=\"flex-1 border-t border-border/40\" />\n    </div>\n  );\n};\n\nexport default FormDivider;\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAUA,MAAM,cAA0C,CAAC,EAC/C,OAAO,IAAI,EACX,SAAS,EACV;IACC,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;;0BAC/C,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAK,WAAU;8BACb;;;;;;;;;;;0BAGL,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;KAfM;uCAiBS", "debugId": null}}, {"offset": {"line": 956, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/test/dentist-appointment-platform/src/components/forms/RoleSelector.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { UserRole } from '@/types/auth';\nimport { cn } from '@/lib/utils';\nimport { User, Stethoscope } from 'lucide-react';\n\ninterface RoleSelectorProps {\n  value: UserRole | '' | 'PATIENT' | 'DENTIST';\n  onChange: (role: UserRole) => void;\n  error?: string;\n  disabled?: boolean;\n  className?: string;\n}\n\nconst RoleSelector: React.FC<RoleSelectorProps> = ({\n  value,\n  onChange,\n  error,\n  disabled = false,\n  className,\n}) => {\n  const roles = [\n    {\n      value: UserRole.PATIENT,\n      label: 'Patient',\n      description: 'Book appointments and manage your dental care',\n      icon: User,\n    },\n    {\n      value: UserRole.DENTIST,\n      label: 'Dentist',\n      description: 'Manage practice, patients, and appointments',\n      icon: Stethoscope,\n    },\n  ];\n\n  return (\n    <div className={cn('space-y-3', className)}>\n      <label className=\"text-sm font-medium text-foreground\">\n        I am a <span className=\"text-destructive\">*</span>\n      </label>\n      \n      <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-3\">\n        {roles.map((role) => {\n          const Icon = role.icon;\n          const isSelected = value === role.value;\n          \n          return (\n            <button\n              key={role.value}\n              type=\"button\"\n              onClick={() => !disabled && onChange(role.value)}\n              disabled={disabled}\n              className={cn(\n                'p-4 rounded-lg border-2 transition-all duration-200',\n                'text-left hover:bg-accent/50 focus:outline-none focus:ring-2 focus:ring-primary/50',\n                'disabled:opacity-50 disabled:cursor-not-allowed',\n                isSelected\n                  ? 'border-primary bg-primary/10 text-foreground'\n                  : 'border-border/40 bg-background/50 text-muted-foreground hover:text-foreground',\n                error && 'border-destructive'\n              )}\n            >\n              <div className=\"flex items-start space-x-3\">\n                <div className={cn(\n                  'w-8 h-8 rounded-lg flex items-center justify-center',\n                  isSelected ? 'bg-primary/20 text-primary' : 'bg-muted text-muted-foreground'\n                )}>\n                  <Icon className=\"w-4 h-4\" />\n                </div>\n                <div className=\"flex-1 min-w-0\">\n                  <h3 className={cn(\n                    'font-medium text-sm',\n                    isSelected ? 'text-foreground' : 'text-foreground'\n                  )}>\n                    {role.label}\n                  </h3>\n                  <p className={cn(\n                    'text-xs mt-1',\n                    isSelected ? 'text-muted-foreground' : 'text-muted-foreground'\n                  )}>\n                    {role.description}\n                  </p>\n                </div>\n              </div>\n            </button>\n          );\n        })}\n      </div>\n      \n      {error && (\n        <p className=\"text-sm text-destructive\" role=\"alert\">\n          {error}\n        </p>\n      )}\n    </div>\n  );\n};\n\nexport default RoleSelector;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AALA;;;;;AAeA,MAAM,eAA4C,CAAC,EACjD,KAAK,EACL,QAAQ,EACR,KAAK,EACL,WAAW,KAAK,EAChB,SAAS,EACV;IACC,MAAM,QAAQ;QACZ;YACE,OAAO,uHAAA,CAAA,WAAQ,CAAC,OAAO;YACvB,OAAO;YACP,aAAa;YACb,MAAM,qMAAA,CAAA,OAAI;QACZ;QACA;YACE,OAAO,uHAAA,CAAA,WAAQ,CAAC,OAAO;YACvB,OAAO;YACP,aAAa;YACb,MAAM,mNAAA,CAAA,cAAW;QACnB;KACD;IAED,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;0BAC9B,6LAAC;gBAAM,WAAU;;oBAAsC;kCAC9C,6LAAC;wBAAK,WAAU;kCAAmB;;;;;;;;;;;;0BAG5C,6LAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC;oBACV,MAAM,OAAO,KAAK,IAAI;oBACtB,MAAM,aAAa,UAAU,KAAK,KAAK;oBAEvC,qBACE,6LAAC;wBAEC,MAAK;wBACL,SAAS,IAAM,CAAC,YAAY,SAAS,KAAK,KAAK;wBAC/C,UAAU;wBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uDACA,sFACA,mDACA,aACI,iDACA,iFACJ,SAAS;kCAGX,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,uDACA,aAAa,+BAA+B;8CAE5C,cAAA,6LAAC;wCAAK,WAAU;;;;;;;;;;;8CAElB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACd,uBACA,aAAa,oBAAoB;sDAEhC,KAAK,KAAK;;;;;;sDAEb,6LAAC;4CAAE,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACb,gBACA,aAAa,0BAA0B;sDAEtC,KAAK,WAAW;;;;;;;;;;;;;;;;;;uBAhClB,KAAK,KAAK;;;;;gBAsCrB;;;;;;YAGD,uBACC,6LAAC;gBAAE,WAAU;gBAA2B,MAAK;0BAC1C;;;;;;;;;;;;AAKX;KAnFM;uCAqFS", "debugId": null}}, {"offset": {"line": 1103, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/test/dentist-appointment-platform/src/components/forms/TermsCheckbox.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { Check } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\ninterface TermsCheckboxProps {\n  checked: boolean;\n  onChange: (checked: boolean) => void;\n  error?: string;\n  disabled?: boolean;\n  className?: string;\n}\n\nconst TermsCheckbox: React.FC<TermsCheckboxProps> = ({\n  checked,\n  onChange,\n  error,\n  disabled = false,\n  className,\n}) => {\n  return (\n    <div className={cn('space-y-2', className)}>\n      <div className=\"flex items-start space-x-3\">\n        <button\n          type=\"button\"\n          onClick={() => !disabled && onChange(!checked)}\n          disabled={disabled}\n          className={cn(\n            'w-5 h-5 rounded border-2 flex items-center justify-center transition-all duration-200',\n            'focus:outline-none focus:ring-2 focus:ring-primary/50',\n            'disabled:opacity-50 disabled:cursor-not-allowed',\n            checked\n              ? 'bg-primary border-primary text-primary-foreground'\n              : 'border-border/40 bg-background hover:border-primary/50',\n            error && 'border-destructive'\n          )}\n          aria-checked={checked}\n          role=\"checkbox\"\n        >\n          {checked && <Check className=\"w-3 h-3\" />}\n        </button>\n        \n        <div className=\"flex-1 text-sm\">\n          <label className={cn(\n            'text-muted-foreground leading-relaxed cursor-pointer',\n            disabled && 'cursor-not-allowed'\n          )}>\n            I agree to the{' '}\n            <Link\n              href=\"/legal/terms\"\n              className=\"text-primary hover:text-primary/80 underline\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n            >\n              Terms of Service\n            </Link>\n            {' '}and{' '}\n            <Link\n              href=\"/legal/privacy\"\n              className=\"text-primary hover:text-primary/80 underline\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n            >\n              Privacy Policy\n            </Link>\n            {' '}\n            <span className=\"text-destructive\">*</span>\n          </label>\n        </div>\n      </div>\n      \n      {error && (\n        <p className=\"text-sm text-destructive ml-8\" role=\"alert\">\n          {error}\n        </p>\n      )}\n    </div>\n  );\n};\n\nexport default TermsCheckbox;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AALA;;;;;AAeA,MAAM,gBAA8C,CAAC,EACnD,OAAO,EACP,QAAQ,EACR,KAAK,EACL,WAAW,KAAK,EAChB,SAAS,EACV;IACC,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;0BAC9B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,MAAK;wBACL,SAAS,IAAM,CAAC,YAAY,SAAS,CAAC;wBACtC,UAAU;wBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yFACA,yDACA,mDACA,UACI,sDACA,0DACJ,SAAS;wBAEX,gBAAc;wBACd,MAAK;kCAEJ,yBAAW,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;;;;;;kCAG/B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACjB,wDACA,YAAY;;gCACX;gCACc;8CACf,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;oCACV,QAAO;oCACP,KAAI;8CACL;;;;;;gCAGA;gCAAI;gCAAI;8CACT,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;oCACV,QAAO;oCACP,KAAI;8CACL;;;;;;gCAGA;8CACD,6LAAC;oCAAK,WAAU;8CAAmB;;;;;;;;;;;;;;;;;;;;;;;YAKxC,uBACC,6LAAC;gBAAE,WAAU;gBAAgC,MAAK;0BAC/C;;;;;;;;;;;;AAKX;KAjEM;uCAmES", "debugId": null}}, {"offset": {"line": 1228, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/test/dentist-appointment-platform/src/components/forms/index.ts"], "sourcesContent": ["export { default as AuthFormField } from './AuthFormField';\nexport { default as AuthSubmitButton } from './AuthSubmitButton';\nexport { default as OAuthButton } from './OAuthButton';\nexport { default as FormDivider } from './FormDivider';\nexport { default as RoleSelector } from './RoleSelector';\nexport { default as TermsCheckbox } from './TermsCheckbox';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 1304, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/test/dentist-appointment-platform/src/lib/validations/auth.ts"], "sourcesContent": ["import { z } from 'zod';\n\n// Login validation schema\nexport const loginSchema = z.object({\n  email: z\n    .string()\n    .min(1, 'Email is required')\n    .email('Please enter a valid email address'),\n  password: z\n    .string()\n    .min(1, 'Password is required')\n    .min(8, 'Password must be at least 8 characters long'),\n});\n\n// Registration validation schema\nexport const registerSchema = z.object({\n  firstName: z\n    .string()\n    .min(1, 'First name is required')\n    .min(2, 'First name must be at least 2 characters')\n    .max(50, 'First name must be less than 50 characters'),\n  lastName: z\n    .string()\n    .min(1, 'Last name is required')\n    .min(2, 'Last name must be at least 2 characters')\n    .max(50, 'Last name must be less than 50 characters'),\n  email: z\n    .string()\n    .min(1, 'Email is required')\n    .email('Please enter a valid email address'),\n  password: z\n    .string()\n    .min(8, 'Password must be at least 8 characters long')\n    .regex(\n      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]/,\n      'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'\n    ),\n  confirmPassword: z\n    .string()\n    .min(1, 'Please confirm your password'),\n  role: z.union([z.enum(['PATIENT', 'DENTIST']), z.literal('')], {\n    required_error: 'Please select a role',\n  }).refine((val) => val !== '', {\n    message: 'Please select a role',\n  }),\n  phone: z\n    .string()\n    .optional()\n    .refine((val) => !val || /^\\+?[\\d\\s\\-\\(\\)]+$/.test(val), {\n      message: 'Please enter a valid phone number',\n    }),\n  acceptTerms: z\n    .boolean()\n    .refine((val) => val === true, {\n      message: 'You must accept the terms and conditions',\n    }),\n}).refine((data) => data.password === data.confirmPassword, {\n  message: 'Passwords do not match',\n  path: ['confirmPassword'],\n});\n\n// Forgot password validation schema\nexport const forgotPasswordSchema = z.object({\n  email: z\n    .string()\n    .min(1, 'Email is required')\n    .email('Please enter a valid email address'),\n});\n\n// Reset password validation schema\nexport const resetPasswordSchema = z.object({\n  password: z\n    .string()\n    .min(8, 'Password must be at least 8 characters long')\n    .regex(\n      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]/,\n      'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'\n    ),\n  confirmPassword: z\n    .string()\n    .min(1, 'Please confirm your password'),\n}).refine((data) => data.password === data.confirmPassword, {\n  message: 'Passwords do not match',\n  path: ['confirmPassword'],\n});\n\n// Type exports\nexport type LoginFormData = z.infer<typeof loginSchema>;\nexport type RegisterFormData = z.infer<typeof registerSchema>;\nexport type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>;\nexport type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>;\n"], "names": [], "mappings": ";;;;;;AAAA;AAAA;;AAGO,MAAM,cAAc,oLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAClC,OAAO,oLAAA,CAAA,IAAC,CACL,MAAM,GACN,GAAG,CAAC,GAAG,qBACP,KAAK,CAAC;IACT,UAAU,oLAAA,CAAA,IAAC,CACR,MAAM,GACN,GAAG,CAAC,GAAG,wBACP,GAAG,CAAC,GAAG;AACZ;AAGO,MAAM,iBAAiB,oLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACrC,WAAW,oLAAA,CAAA,IAAC,CACT,MAAM,GACN,GAAG,CAAC,GAAG,0BACP,GAAG,CAAC,GAAG,4CACP,GAAG,CAAC,IAAI;IACX,UAAU,oLAAA,CAAA,IAAC,CACR,MAAM,GACN,GAAG,CAAC,GAAG,yBACP,GAAG,CAAC,GAAG,2CACP,GAAG,CAAC,IAAI;IACX,OAAO,oLAAA,CAAA,IAAC,CACL,MAAM,GACN,GAAG,CAAC,GAAG,qBACP,KAAK,CAAC;IACT,UAAU,oLAAA,CAAA,IAAC,CACR,MAAM,GACN,GAAG,CAAC,GAAG,+CACP,KAAK,CACJ,mEACA;IAEJ,iBAAiB,oLAAA,CAAA,IAAC,CACf,MAAM,GACN,GAAG,CAAC,GAAG;IACV,MAAM,oLAAA,CAAA,IAAC,CAAC,KAAK,CAAC;QAAC,oLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAW;SAAU;QAAG,oLAAA,CAAA,IAAC,CAAC,OAAO,CAAC;KAAI,EAAE;QAC7D,gBAAgB;IAClB,GAAG,MAAM,CAAC,CAAC,MAAQ,QAAQ,IAAI;QAC7B,SAAS;IACX;IACA,OAAO,oLAAA,CAAA,IAAC,CACL,MAAM,GACN,QAAQ,GACR,MAAM,CAAC,CAAC,MAAQ,CAAC,OAAO,qBAAqB,IAAI,CAAC,MAAM;QACvD,SAAS;IACX;IACF,aAAa,oLAAA,CAAA,IAAC,CACX,OAAO,GACP,MAAM,CAAC,CAAC,MAAQ,QAAQ,MAAM;QAC7B,SAAS;IACX;AACJ,GAAG,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ,KAAK,KAAK,eAAe,EAAE;IAC1D,SAAS;IACT,MAAM;QAAC;KAAkB;AAC3B;AAGO,MAAM,uBAAuB,oLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3C,OAAO,oLAAA,CAAA,IAAC,CACL,MAAM,GACN,GAAG,CAAC,GAAG,qBACP,KAAK,CAAC;AACX;AAGO,MAAM,sBAAsB,oLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC1C,UAAU,oLAAA,CAAA,IAAC,CACR,MAAM,GACN,GAAG,CAAC,GAAG,+CACP,KAAK,CACJ,mEACA;IAEJ,iBAAiB,oLAAA,CAAA,IAAC,CACf,MAAM,GACN,GAAG,CAAC,GAAG;AACZ,GAAG,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ,KAAK,KAAK,eAAe,EAAE;IAC1D,SAAS;IACT,MAAM;QAAC;KAAkB;AAC3B", "debugId": null}}, {"offset": {"line": 1367, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/test/dentist-appointment-platform/src/app/auth/login/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { toast } from 'sonner';\nimport { signIn } from 'next-auth/react';\n\nimport AuthLayout from '@/components/layout/AuthLayout';\nimport { AuthFormField, AuthSubmitButton, OAuthButton, FormDivider } from '@/components/forms';\nimport { loginSchema, type LoginFormData } from '@/lib/validations/auth';\n\nconst LoginPage: React.FC = () => {\n  const router = useRouter();\n  const [isLoading, setIsLoading] = useState(false);\n  const [isOAuthLoading, setIsOAuthLoading] = useState(false);\n\n  const {\n    handleSubmit,\n    formState: { errors },\n    setValue,\n    watch,\n  } = useForm<LoginFormData>({\n    resolver: zod<PERSON><PERSON><PERSON>ver(loginSchema),\n    defaultValues: {\n      email: '',\n      password: '',\n    },\n  });\n\n  const watchedValues = watch();\n\n  const onSubmit = async (data: LoginFormData) => {\n    try {\n      setIsLoading(true);\n\n      // Try NextAuth credentials login first\n      const result = await signIn('credentials', {\n        email: data.email,\n        password: data.password,\n        redirect: false,\n      });\n\n      if (result?.error) {\n        toast.error('Login failed', {\n          description: 'Please check your credentials and try again.',\n        });\n      } else {\n        toast.success('Login successful!', {\n          description: 'Welcome back to DentCare Pro.',\n        });\n        router.push('/dashboard');\n      }\n    } catch (error) {\n      toast.error('Login failed', {\n        description: error instanceof Error ? error.message : 'Please check your credentials and try again.',\n      });\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleGoogleLogin = async () => {\n    try {\n      setIsOAuthLoading(true);\n\n      const result = await signIn('google', {\n        callbackUrl: '/dashboard',\n        redirect: false,\n      });\n\n      if (result?.error) {\n        toast.error('OAuth login failed', {\n          description: 'Please try again or use email/password login.',\n        });\n      } else if (result?.url) {\n        toast.success('Login successful!', {\n          description: 'Redirecting to dashboard...',\n        });\n        router.push(result.url);\n      }\n    } catch (oauthError) {\n      console.error('OAuth login error:', oauthError);\n      toast.error('OAuth login failed', {\n        description: 'Please try again or use email/password login.',\n      });\n    } finally {\n      setIsOAuthLoading(false);\n    }\n  };\n\n  return (\n    <AuthLayout\n      title=\"Welcome back\"\n      subtitle=\"Sign in to your DentCare Pro account\"\n    >\n      <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n        {/* Email Field */}\n        <AuthFormField\n          label=\"Email address\"\n          type=\"email\"\n          placeholder=\"Enter your email\"\n          value={watchedValues.email}\n          onChange={(value) => setValue('email', value)}\n          error={errors.email?.message}\n          required\n          autoComplete=\"email\"\n        />\n\n        {/* Password Field */}\n        <AuthFormField\n          label=\"Password\"\n          type=\"password\"\n          placeholder=\"Enter your password\"\n          value={watchedValues.password}\n          onChange={(value) => setValue('password', value)}\n          error={errors.password?.message}\n          required\n          autoComplete=\"current-password\"\n        />\n\n        {/* Forgot Password Link */}\n        <div className=\"flex justify-end\">\n          <Link\n            href=\"/auth/forgot-password\"\n            className=\"text-sm text-primary hover:text-primary/80 transition-colors\"\n          >\n            Forgot your password?\n          </Link>\n        </div>\n\n        {/* Submit Button */}\n        <AuthSubmitButton\n          isLoading={isLoading}\n          disabled={isLoading || isOAuthLoading}\n          loadingText=\"Signing in...\"\n        >\n          Sign in\n        </AuthSubmitButton>\n\n        {/* Divider */}\n        <FormDivider />\n\n        {/* OAuth Button */}\n        <OAuthButton\n          provider=\"google\"\n          isLoading={isOAuthLoading}\n          disabled={isLoading || isOAuthLoading}\n          onClick={handleGoogleLogin}\n        />\n\n        {/* Test Credentials */}\n        <div className=\"p-4 bg-muted/50 rounded-lg\">\n          <h3 className=\"text-sm font-medium text-foreground mb-2\">Test Credentials</h3>\n          <div className=\"text-xs text-muted-foreground space-y-1\">\n            <div><strong>Patient:</strong> <EMAIL> / password123</div>\n            <div><strong>Dentist:</strong> <EMAIL> / password123</div>\n            <div><strong>Admin:</strong> <EMAIL> / password123</div>\n          </div>\n        </div>\n\n        {/* Sign Up Link */}\n        <div className=\"text-center\">\n          <p className=\"text-sm text-muted-foreground\">\n            Don&apos;t have an account?{' '}\n            <Link\n              href=\"/auth/register\"\n              className=\"text-primary hover:text-primary/80 transition-colors font-medium\"\n            >\n              Sign up\n            </Link>\n          </p>\n        </div>\n      </form>\n    </AuthLayout>\n  );\n};\n\nexport default LoginPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAZA;;;;;;;;;;;AAcA,MAAM,YAAsB;;IAC1B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,EACJ,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACrB,QAAQ,EACR,KAAK,EACN,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAiB;QACzB,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE,oIAAA,CAAA,cAAW;QACjC,eAAe;YACb,OAAO;YACP,UAAU;QACZ;IACF;IAEA,MAAM,gBAAgB;IAEtB,MAAM,WAAW,OAAO;QACtB,IAAI;YACF,aAAa;YAEb,uCAAuC;YACvC,MAAM,SAAS,MAAM,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,eAAe;gBACzC,OAAO,KAAK,KAAK;gBACjB,UAAU,KAAK,QAAQ;gBACvB,UAAU;YACZ;YAEA,IAAI,QAAQ,OAAO;gBACjB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,gBAAgB;oBAC1B,aAAa;gBACf;YACF,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,qBAAqB;oBACjC,aAAa;gBACf;gBACA,OAAO,IAAI,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,gBAAgB;gBAC1B,aAAa,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACxD;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI;YACF,kBAAkB;YAElB,MAAM,SAAS,MAAM,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,UAAU;gBACpC,aAAa;gBACb,UAAU;YACZ;YAEA,IAAI,QAAQ,OAAO;gBACjB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,sBAAsB;oBAChC,aAAa;gBACf;YACF,OAAO,IAAI,QAAQ,KAAK;gBACtB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,qBAAqB;oBACjC,aAAa;gBACf;gBACA,OAAO,IAAI,CAAC,OAAO,GAAG;YACxB;QACF,EAAE,OAAO,YAAY;YACnB,QAAQ,KAAK,CAAC,sBAAsB;YACpC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,sBAAsB;gBAChC,aAAa;YACf;QACF,SAAU;YACR,kBAAkB;QACpB;IACF;IAEA,qBACE,6LAAC,6IAAA,CAAA,UAAU;QACT,OAAM;QACN,UAAS;kBAET,cAAA,6LAAC;YAAK,UAAU,aAAa;YAAW,WAAU;;8BAEhD,6LAAC,2LAAA,CAAA,gBAAa;oBACZ,OAAM;oBACN,MAAK;oBACL,aAAY;oBACZ,OAAO,cAAc,KAAK;oBAC1B,UAAU,CAAC,QAAU,SAAS,SAAS;oBACvC,OAAO,OAAO,KAAK,EAAE;oBACrB,QAAQ;oBACR,cAAa;;;;;;8BAIf,6LAAC,2LAAA,CAAA,gBAAa;oBACZ,OAAM;oBACN,MAAK;oBACL,aAAY;oBACZ,OAAO,cAAc,QAAQ;oBAC7B,UAAU,CAAC,QAAU,SAAS,YAAY;oBAC1C,OAAO,OAAO,QAAQ,EAAE;oBACxB,QAAQ;oBACR,cAAa;;;;;;8BAIf,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;kCACX;;;;;;;;;;;8BAMH,6LAAC,iMAAA,CAAA,mBAAgB;oBACf,WAAW;oBACX,UAAU,aAAa;oBACvB,aAAY;8BACb;;;;;;8BAKD,6LAAC,uLAAA,CAAA,cAAW;;;;;8BAGZ,6LAAC,uLAAA,CAAA,cAAW;oBACV,UAAS;oBACT,WAAW;oBACX,UAAU,aAAa;oBACvB,SAAS;;;;;;8BAIX,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA2C;;;;;;sCACzD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDAAI,6LAAC;sDAAO;;;;;;wCAAiB;;;;;;;8CAC9B,6LAAC;;sDAAI,6LAAC;sDAAO;;;;;;wCAAiB;;;;;;;8CAC9B,6LAAC;;sDAAI,6LAAC;sDAAO;;;;;;wCAAe;;;;;;;;;;;;;;;;;;;8BAKhC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;;4BAAgC;4BACf;0CAC5B,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GApKM;;QACW,qIAAA,CAAA,YAAS;QASpB,iKAAA,CAAA,UAAO;;;KAVP;uCAsKS", "debugId": null}}]}