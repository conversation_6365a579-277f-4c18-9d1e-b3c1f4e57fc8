(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["chunks/[root-of-the-server]__de110ef0._.js", {

"[externals]/node:buffer [external] (node:buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[project]/middleware.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "config": (()=>config),
    "middleware": (()=>middleware)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$server$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/api/server.js [middleware-edge] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/web/spec-extension/response.js [middleware-edge] (ecmascript)");
;
// Type guard to check if config has roles
function hasRoles(config) {
    return 'roles' in config && Array.isArray(config.roles);
}
// Define protected routes and their requirements
const protectedRoutes = {
    // Dashboard routes
    '/dashboard': {
        requireAuth: true
    },
    '/patient': {
        requireAuth: true,
        roles: [
            'PATIENT'
        ]
    },
    '/dentist': {
        requireAuth: true,
        roles: [
            'DENTIST'
        ]
    },
    '/admin': {
        requireAuth: true,
        roles: [
            'ADMIN'
        ]
    },
    // Profile and settings
    '/profile': {
        requireAuth: true
    },
    '/settings': {
        requireAuth: true
    },
    // Appointments
    '/appointments': {
        requireAuth: true
    },
    // Patients (dentist/admin only)
    '/patients': {
        requireAuth: true,
        roles: [
            'DENTIST',
            'ADMIN'
        ]
    },
    // Documents
    '/documents': {
        requireAuth: true
    },
    // Reports (dentist/admin only)
    '/reports': {
        requireAuth: true,
        roles: [
            'DENTIST',
            'ADMIN'
        ]
    }
};
// Public routes that don't require authentication
const publicRoutes = [
    '/',
    '/auth/login',
    '/auth/register',
    '/auth/forgot-password',
    '/auth/reset-password',
    '/legal/terms',
    '/legal/privacy',
    '/help',
    '/contact'
];
// Auth routes that should redirect if already authenticated
const authRoutes = [
    '/auth/login',
    '/auth/register',
    '/auth/forgot-password'
];
function middleware(request) {
    const { pathname } = request.nextUrl;
    // Skip middleware for static files and API routes
    if (pathname.startsWith('/_next') || pathname.startsWith('/api') || pathname.includes('.') || pathname.startsWith('/favicon')) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
    }
    // Get authentication status from cookies/headers
    // Note: In a real implementation, you'd verify the JWT token here
    const authToken = request.cookies.get('auth-token')?.value;
    const userRole = request.cookies.get('user-role')?.value;
    const isAuthenticated = !!authToken;
    // Check if route is public
    if (publicRoutes.includes(pathname)) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
    }
    // Handle auth routes (redirect if already authenticated)
    if (authRoutes.includes(pathname) && isAuthenticated) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL('/dashboard', request.url));
    }
    // Check protected routes
    const matchedRoute = Object.entries(protectedRoutes).find(([route])=>pathname.startsWith(route));
    if (matchedRoute) {
        const [, config] = matchedRoute;
        // Check authentication requirement
        if (config.requireAuth && !isAuthenticated) {
            const loginUrl = new URL('/auth/login', request.url);
            loginUrl.searchParams.set('redirect', pathname);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(loginUrl);
        }
        // Check role requirements using type guard
        if (hasRoles(config) && userRole && !config.roles.includes(userRole)) {
            // Redirect to appropriate dashboard based on user role
            let redirectPath = '/dashboard';
            switch(userRole){
                case 'PATIENT':
                    redirectPath = '/patient/dashboard';
                    break;
                case 'DENTIST':
                    redirectPath = '/dentist/dashboard';
                    break;
                case 'ADMIN':
                    redirectPath = '/admin/dashboard';
                    break;
            }
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL(redirectPath, request.url));
        }
    }
    // Default: allow the request to continue
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
}
const config = {
    matcher: [
        /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */ '/((?!api|_next/static|_next/image|favicon.ico).*)'
    ]
};
}}),
}]);

//# sourceMappingURL=%5Broot-of-the-server%5D__de110ef0._.js.map