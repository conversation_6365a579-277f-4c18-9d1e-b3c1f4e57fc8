"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[60],{285:(e,t,r)=>{r.d(t,{$:()=>l});var a=r(5155);r(2115);var n=r(9708),o=r(2085),s=r(2911);let i=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:r,size:o,asChild:l=!1,...c}=e,d=l?n.DX:"button";return(0,a.jsx)(d,{"data-slot":"button",className:(0,s.cn)(i({variant:r,size:o,className:t})),...c})}},2911:(e,t,r)=>{r.d(t,{cn:()=>s,DR:()=>i});var a=r(2596),n=r(9688);r(9509).env.NEXT_PUBLIC_API_URL,Array.from({length:48},(e,t)=>{let r=Math.floor(t/2),a=t%2==0?"00":"30",n="".concat(r.toString().padStart(2,"0"),":").concat(a),o=0===r?12:r>12?r-12:r,s=r<12?"AM":"PM";return{value:n,label:"".concat(o,":").concat(a," ").concat(s)}});let o={PASSWORD_MIN_LENGTH:8,NAME_MIN_LENGTH:2,NAME_MAX_LENGTH:50,DESCRIPTION_MAX_LENGTH:1e3,NOTES_MAX_LENGTH:2e3};function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n.QP)((0,a.$)(t))}o.PASSWORD_MIN_LENGTH,o.NAME_MIN_LENGTH,o.NAME_MAX_LENGTH,o.DESCRIPTION_MAX_LENGTH,o.NOTES_MAX_LENGTH;let i={capitalize:e=>e.charAt(0).toUpperCase()+e.slice(1).toLowerCase(),capitalizeWords:e=>e.replace(/\w\S*/g,e=>e.charAt(0).toUpperCase()+e.substr(1).toLowerCase()),truncate:function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"...";return e.length<=t?e:e.substring(0,t)+r},slugify:e=>e.toLowerCase().replace(/[^\w\s-]/g,"").replace(/[\s_-]+/g,"-").replace(/^-+|-+$/g,""),initials:e=>e.split(" ").map(e=>e.charAt(0)).join("").toUpperCase().substring(0,2),formatPhoneNumber:e=>{let t=e.replace(/\D/g,"");return 10===t.length?"(".concat(t.slice(0,3),") ").concat(t.slice(3,6),"-").concat(t.slice(6)):e},maskEmail:e=>{let[t,r]=e.split("@");if(t.length<=2)return e;let a=t.charAt(0)+"*".repeat(t.length-2)+t.charAt(t.length-1);return"".concat(a,"@").concat(r)}}},3294:(e,t,r)=>{r.d(t,{As:()=>c,BG:()=>d,nc:()=>l});var a=r(5453),n=r(6786),o=r(9385),s=r(2108);let i={[o.g.PATIENT]:["appointments:read:own","appointments:create:own","appointments:update:own","documents:read:own","profile:read:own","profile:update:own"],[o.g.DENTIST]:["appointments:read:all","appointments:create:all","appointments:update:all","appointments:delete:all","patients:read:all","patients:update:all","documents:read:all","documents:create:all","documents:update:all","documents:delete:all","profile:read:own","profile:update:own"],[o.g.ADMIN]:["*"]},l=(0,a.v)()((0,n.Zr)((e,t)=>({user:null,session:null,isAuthenticated:!1,isLoading:!1,error:null,setUser:t=>{e({user:t,isAuthenticated:!!t,error:null})},setSession:t=>{e({session:t,user:(null==t?void 0:t.user)||null,isAuthenticated:!!(null==t?void 0:t.user),error:null})},setLoading:t=>{e({isLoading:t})},setError:t=>{e({error:t,isLoading:!1})},login:async(e,r)=>{let{setLoading:a,setError:n,setSession:o}=t();try{a(!0),n(null);let t=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:r})});if(!t.ok)throw Error("Login failed");let s=await t.json();if(s.success&&s.user&&s.token){let e={user:s.user,accessToken:s.token,expiresAt:new Date(Date.now()+864e5)};o(e)}else throw Error(s.message||"Login failed")}catch(e){n(e instanceof Error?e.message:"Login failed")}finally{a(!1)}},logout:async()=>{try{await (0,s.signOut)({redirect:!1})}catch(e){console.error("Logout error:",e)}e({user:null,session:null,isAuthenticated:!1,error:null})},refreshToken:async()=>{let{session:e,setSession:r,setError:a}=t();if(null==e?void 0:e.refreshToken)try{let t=await fetch("/api/auth/refresh",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refreshToken:e.refreshToken})});if(!t.ok)throw Error("Token refresh failed");let a=await t.json();if(a.success&&a.token){let t={...e,accessToken:a.token,expiresAt:new Date(Date.now()+864e5)};r(t)}else throw Error("Token refresh failed")}catch(e){a(e instanceof Error?e.message:"Token refresh failed"),t().logout()}},updateProfile:async e=>{let{user:r,session:a,setUser:n,setError:o,setLoading:s}=t();if(!r||!a)return void o("User not authenticated");try{s(!0),o(null);let t=await fetch("/api/user/profile",{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(a.accessToken)},body:JSON.stringify(e)});if(!t.ok)throw Error("Profile update failed");let r=await t.json();if(r.success&&r.user)n(r.user);else throw Error(r.message||"Profile update failed")}catch(e){o(e instanceof Error?e.message:"Profile update failed")}finally{s(!1)}},checkPermission:e=>{let{user:r}=t();if(!r)return!1;let a=i[r.role]||[];return!!a.includes("*")||a.includes(e)},hasRole:e=>{let{user:r}=t();return(null==r?void 0:r.role)===e},clearError:()=>{e({error:null})},reset:()=>{e({user:null,session:null,isAuthenticated:!1,isLoading:!1,error:null})}}),{name:"auth-storage",storage:(0,n.KU)(()=>localStorage),partialize:e=>({user:e.user,session:e.session,isAuthenticated:e.isAuthenticated})})),c=()=>{let e=l();return{user:e.user,session:e.session,isAuthenticated:e.isAuthenticated,isLoading:e.isLoading,error:e.error}},d=()=>{let e=l();return{login:e.login,logout:e.logout,refreshToken:e.refreshToken,updateProfile:e.updateProfile,checkPermission:e.checkPermission,hasRole:e.hasRole,clearError:e.clearError,reset:e.reset}}},6695:(e,t,r)=>{r.d(t,{BT:()=>l,Wu:()=>c,ZB:()=>i,Zp:()=>o,aR:()=>s});var a=r(5155);r(2115);var n=r(2911);function o(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function s(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function i(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",t),...r})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",t),...r})}function c(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",t),...r})}},9385:(e,t,r)=>{r.d(t,{g:()=>a});var a=function(e){return e.PATIENT="PATIENT",e.DENTIST="DENTIST",e.ADMIN="ADMIN",e}({})}}]);