{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_9dd07395._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_24e2925a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "dCymQpMHRcpc6k4spZmvKuqTLzAU7j7vm2+V82BHnt0=", "__NEXT_PREVIEW_MODE_ID": "725e642191970807c6f6f0dd30ee4940", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "44d28ae386b90e18254f101119643867c42b243ee72f09a9f3e2d9c6f1029490", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "dbb45a34c2fc2fa6e00304201a1aa1700958cfe3d801e9ec62264d0ecd99592c"}}}, "instrumentation": null, "functions": {}}