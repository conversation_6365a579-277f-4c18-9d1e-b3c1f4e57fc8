(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[983],{6117:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>h});var s=a(5155),o=a(2115),t=a(5695),l=a(6874),i=a.n(l),n=a(2177),d=a(8778),c=a(6671),u=a(2108),m=a(1929),p=a(6434),g=a(742);let h=()=>{var e,r,a,l,h,y,v,f;let x=(0,t.useRouter)(),[C,w]=(0,o.useState)(!1),[b,j]=(0,o.useState)(!1),{handleSubmit:N,formState:{errors:P},setValue:R,watch:E}=(0,n.mN)({resolver:(0,d.u)(g.z<PERSON>),defaultValues:{firstName:"",lastName:"",email:"",password:"",confirmPassword:"",role:"",phone:"",acceptTerms:!1}}),k=E(),S=async e=>{try{w(!0),console.log("Registration data:",e),c.oR.success("Registration successful!",{description:"Please check your email to verify your account."}),x.push("/auth/login?message=registration-success")}catch(e){c.oR.error("Registration failed",{description:e instanceof Error?e.message:"Please try again."})}finally{w(!1)}},q=async()=>{try{j(!0);let e=await (0,u.signIn)("google",{callbackUrl:"/dashboard",redirect:!1});(null==e?void 0:e.error)?c.oR.error("OAuth registration failed",{description:"Please try again or use the registration form."}):(null==e?void 0:e.url)&&(c.oR.success("Registration successful!",{description:"Welcome to DentCare Pro!"}),x.push(e.url))}catch(e){console.error("OAuth registration error:",e),c.oR.error("OAuth registration failed",{description:"Please try again or use the registration form."})}finally{j(!1)}};return(0,s.jsx)(m.A,{title:"Create your account",subtitle:"Join DentCare Pro and start managing your dental care",children:(0,s.jsxs)("form",{onSubmit:N(S),className:"space-y-6",children:[(0,s.jsx)(p.ID,{provider:"google",isLoading:b,disabled:C||b,onClick:q,children:"Sign up with Google"}),(0,s.jsx)(p.Sq,{text:"or sign up with email"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[(0,s.jsx)(p.vy,{label:"First name",type:"text",placeholder:"Enter your first name",value:k.firstName,onChange:e=>R("firstName",e),error:null==(e=P.firstName)?void 0:e.message,required:!0,autoComplete:"given-name"}),(0,s.jsx)(p.vy,{label:"Last name",type:"text",placeholder:"Enter your last name",value:k.lastName,onChange:e=>R("lastName",e),error:null==(r=P.lastName)?void 0:r.message,required:!0,autoComplete:"family-name"})]}),(0,s.jsx)(p.vy,{label:"Email address",type:"email",placeholder:"Enter your email",value:k.email,onChange:e=>R("email",e),error:null==(a=P.email)?void 0:a.message,required:!0,autoComplete:"email"}),(0,s.jsx)(p.vy,{label:"Phone number",type:"tel",placeholder:"Enter your phone number (optional)",value:k.phone||"",onChange:e=>R("phone",e),error:null==(l=P.phone)?void 0:l.message,autoComplete:"tel"}),(0,s.jsx)(p.QT,{value:k.role,onChange:e=>R("role",e),error:null==(h=P.role)?void 0:h.message,disabled:C||b}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(p.vy,{label:"Password",type:"password",placeholder:"Create a strong password",value:k.password,onChange:e=>R("password",e),error:null==(y=P.password)?void 0:y.message,required:!0,autoComplete:"new-password"}),(0,s.jsx)(p.vy,{label:"Confirm password",type:"password",placeholder:"Confirm your password",value:k.confirmPassword,onChange:e=>R("confirmPassword",e),error:null==(v=P.confirmPassword)?void 0:v.message,required:!0,autoComplete:"new-password"})]}),(0,s.jsx)(p.PD,{checked:k.acceptTerms,onChange:e=>R("acceptTerms",e),error:null==(f=P.acceptTerms)?void 0:f.message,disabled:C||b}),(0,s.jsx)(p.FX,{isLoading:C,disabled:C||b,loadingText:"Creating account...",children:"Create account"}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Already have an account?"," ",(0,s.jsx)(i(),{href:"/auth/login",className:"text-primary hover:text-primary/80 transition-colors font-medium",children:"Sign in"})]})})]})})}},8433:(e,r,a)=>{Promise.resolve().then(a.bind(a,6117))}},e=>{var r=r=>e(e.s=r);e.O(0,[52,919,3,981,571,441,684,358],()=>r(8433)),_N_E=e.O()}]);